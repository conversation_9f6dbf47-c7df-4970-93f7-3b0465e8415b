<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.sTime"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <div
      id="order-delivery-chart"
      class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
    ></div>
    <div class="h-[calc(100%-200px-60px-54px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="销售订单号" field="salesOrderNo" />
        <vxe-column title="料号" field="itemCode" />
        <vxe-column title="所属月份" field="deliveryMonth" />
        <vxe-column title="下单日期" field="orderDate" />
        <vxe-column title="预计交期" field="deliveryDate" />
        <vxe-column title="销售数量" field="orderQty" />
        <vxe-column title="累计完成数量" field="totalCompletionQty" />
        <vxe-column title="最后报工日期" field="lastCompletionDate" />
        <vxe-column title="订单状态" field="orderStatus" />
        <vxe-column title="交付率" field="deliveryRate" />
      </vxe-table>
    </div>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="onList"
      size="small"
    />
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { ErpApi } from '@/api/butt-joint/erp'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  dateType: 'year' as any,
  sTime: moment().format('YYYY-MM-DD')
})
const dataList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)

const onList = async () => {
  loading.value = true
  try {
    const res = await ErpApi.getOrderDeliveryList(queryParams)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  onList()
})
</script>
