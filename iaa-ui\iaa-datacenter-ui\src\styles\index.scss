@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.el-collapse {
  --el-collapse-border-color: none !important;
}

.el-dialog{
  padding: 10px !important;

  .el-dialog__header{
    height: 40px !important;

    div{
      height: 40px !important;
      font-size: 14px;
    }
  }
}

.no-modal-mask-layer {
  pointer-events: none;
}
.el-drawer {
  pointer-events: auto;
}

.el-drawer__header{
  padding-top: 10px !important;
  margin-bottom: 10px !important;
  
}

.el-drawer__body{
  padding: 10px !important;
  border-top: 1px solid var(--el-color-primary-light-9);
}

.el-drawer__footer{
  text-align: center !important;
  border-top: 1px solid var(--el-color-primary-light-9);
}

.vxe-table--context-menu-wrapper{
  z-index: 3000 !important;
}

.vxe-cell{
  padding: 5px !important;
}

.custom-form .el-form-item{
  margin: 10px !important;
}

.el-tabs__header.is-top{
  margin:0px !important;
}