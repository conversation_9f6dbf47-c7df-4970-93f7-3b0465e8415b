<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.date"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <el-row>
      <el-col
        v-for="dict in getIntDictOptions('information_person')"
        :key="dict.value"
        :span="6"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        :xl="6"
      >
        <div :id="`person-${dict.value}-line`" class="h-200px"></div>
      </el-col>
    </el-row>
    <div class="h-[calc(100%-200px-44px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="流程模板" field="flowTemplate" width="200" />
        <vxe-column title="流程编码" field="flowNumber" width="100" />
        <vxe-column title="流程名称" field="flowName" min-width="300" align="left" />
        <vxe-column title="流程发起人" field="flowCreator" width="100" />
        <vxe-column title="流程发起时间" field="flowCreateTime" width="150">
          <template #default="{ row }">
            {{ formatToDateTime(row.flowCreateTime) }}
          </template>
        </vxe-column>
        <vxe-column title="流程期望完成时间" field="flowPlanDate" width="150">
          <template #default="{ row }">
            {{ formatToDate(row.flowPlanDate) }}
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="flowType" width="150">
          <template #default="{ row }">
            <el-select
              v-model="row.flowType"
              multiple
              v-if="checkPermi(['report:ekp-flow:save-person'])"
              @change="changePerson(row)"
              co
            >
              <el-option
                v-for="dict in getIntDictOptions('information_person')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <span v-else>{{ getDictLabel('information_person', row.flowType) }}</span>
          </template>
        </vxe-column>
        <vxe-column title="流程结束时间" field="flowPublishTime" width="150">
          <template #default="{ row }">
            {{ row.flowPublishTime ? formatToDateTime(row.flowPublishTime) : '' }}
          </template>
        </vxe-column>
        <vxe-column title="流程状态" field="flowStatus" width="150" />
      </vxe-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { getIntDictOptions, getDictLabel } from '@/utils/dict'
import { FlowApi } from '@/api/report/ekp/flow'
import { formatToDate, formatToDateTime } from '@/utils/dateUtil'
import { checkPermi } from '@/utils/permission'

const queryParams = reactive({
  date: moment().format('YYYY-MM-DD'),
  dateType: 'year' as any
})
const loading = ref(false)
const dataList = ref<any[]>([])
const message = useMessage()

const onList = async () => {
  loading.value = true
  try {
    const res = await FlowApi.getFlowList(queryParams)
    dataList.value = res
  } finally {
    loading.value = false
  }
}

const changePerson = async (row: any) => {
  await FlowApi.saveFlowPerson({
    id: row.flowId,
    person: row.flowType
  })
  message.success('更新成功')
}

onMounted(() => {
  onList()
})
</script>
