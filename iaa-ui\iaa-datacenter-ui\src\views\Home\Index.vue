<template>
  <div>
    <!-- Item slot usage -->
    <GridLayout
      v-model:layout="layout"
      :col-num="12"
      :row-height="30"
      is-draggable
      is-resizable
      vertical-compact
      use-css-transforms
      responsive
    >
      <GridItem
        v-for="item in layout"
        :key="item.i"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :static="item?.static"
        :class="{ 'overflow-y-auto': item?.static }"
      >
        <component :is="componentMap[item.componentName]" />
      </GridItem>
    </GridLayout>
    <!-- MentionsInput 测试区域 -->
    <div class="mt-8 p-6 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-4">MentionsInput 组件测试</h3>

      <div class="space-y-4">
        <!-- 基础使用 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">基础使用（双向绑定）:</label>
          <MentionsInput
            v-model="mentionValue"
            :users="users"
            placeholder="输入 @ 来提及用户..."
            @mention-select="handleMentionSelect"
          />
        </div>

        <!-- 显示当前值 -->
        <div class="p-3 bg-white rounded border">
          <p class="text-sm text-gray-600">当前值: {{ mentionValue || '(空)' }}</p>
        </div>

        <!-- 外部控制 -->
        <div class="flex gap-2">
          <button
            @click="mentionValue = '你好 @张三 ，请查看这个文档。'"
            class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            设置示例文本
          </button>
          <button
            @click="mentionValue = ''"
            class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            清空
          </button>
        </div>

        <!-- 最后选择的用户 -->
        <div v-if="lastSelectedUser" class="p-3 bg-green-50 rounded border border-green-200">
          <p class="text-sm text-green-700">
            最后选择的用户: {{ lastSelectedUser.name }} ({{ lastSelectedUser.email }})
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import welcomeCard from './components/welcome-card.vue'
import shortcutCard from './components/shortcut-card.vue'
import MentionsInput from './components/MentionsInput.vue'
import { GridLayout, GridItem } from 'grid-layout-plus'
import { reactive, ref } from 'vue'

// 定义用户类型
interface User {
  id: string | number;
  name: string;
  email: string;
}

// 定义组件映射
const componentMap = {
  welcomeCard,
  shortcutCard
}

// 测试用户数据
const users: User[] = [
  { id: 1, name: '张三', email: '<EMAIL>' },
  { id: 2, name: '李四', email: '<EMAIL>' },
  { id: 3, name: '王五', email: '<EMAIL>' },
  { id: 4, name: '赵六', email: '<EMAIL>' },
  { id: 5, name: '钱七', email: '<EMAIL>' }
]

// 响应式数据
const mentionValue = ref('')
const lastSelectedUser = ref<User | null>(null)

// 处理用户选择
const handleMentionSelect = (user: User) => {
  lastSelectedUser.value = user
  console.log('选择了用户:', user)
}

// 定义布局
const layout = reactive([
  { x: 9, y: 0, w: 3, h: 3, i: '0', componentName: 'welcomeCard' },
  { x: 0, y: 0, w: 9, h: 12, i: '1', componentName: 'shortcutCard', static: true }
])
</script>

<style scoped lang="scss">
.vgl-layout {
  --vgl-placeholder-bg: red;
  --vgl-placeholder-opacity: 20%;
  --vgl-placeholder-z-index: 2;

  --vgl-item-resizing-z-index: 3;
  --vgl-item-resizing-opacity: 60%;
  --vgl-item-dragging-z-index: 3;
  --vgl-item-dragging-opacity: 100%;

  --vgl-resizer-size: 10px;
  --vgl-resizer-border-color: #444;
  --vgl-resizer-border-width: 2px;
}
.vgl-item--placeholder {
  z-index: var(--vgl-placeholder-z-index, 2);
  user-select: none;
  background-color: var(--vgl-placeholder-bg, red);
  opacity: var(--vgl-placeholder-opacity, 20%);
  transition-duration: 100ms;
}
.vgl-layout {
  --vgl-placeholder-bg: green;
}
// .vgl-layout::before {
//   position: absolute;
//   width: calc(100% - 5px);
//   height: calc(100% - 5px);
//   margin: 5px;
//   content: '';
//   background-image:
//     linear-gradient(to right, lightgrey 1px, transparent 1px),
//     linear-gradient(to bottom, lightgrey 1px, transparent 1px);
//   background-repeat: repeat;
//   background-size: calc(calc(100% - 5px) / 12) 40px;
// }

.vgl-item {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 10px;
}
</style>
