<template>
  <div>
    <!-- Item slot usage -->
    <GridLayout
      v-model:layout="layout"
      :col-num="12"
      :row-height="30"
      is-draggable
      is-resizable
      vertical-compact
      use-css-transforms
      responsive
    >
      <GridItem
        v-for="item in layout"
        :key="item.i"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :static="item?.static"
        :class="{ 'overflow-y-auto': item?.static }"
      >
        <component :is="componentMap[item.componentName]" />
      </GridItem>
    </GridLayout>
    <MentionsInput :users="tempUser" />
  </div>
</template>

<script lang="ts" setup>
import welcomeCard from './components/welcome-card.vue'
import shortcutCard from './components/shortcut-card.vue'
import MentionsInput from './components/MentionsInput.vue'
import { GridLayout, GridItem } from 'grid-layout-plus'
import { reactive } from 'vue'

// 定义组件映射
const componentMap = {
  welcomeCard,
  shortcutCard
}

const tempUser = [{ id: 1, name: '张三', email: '<EMAIL>' }]

// 定义布局
const layout = reactive([
  { x: 9, y: 0, w: 3, h: 3, i: '0', componentName: 'welcomeCard' },
  { x: 0, y: 0, w: 9, h: 12, i: '1', componentName: 'shortcutCard', static: true }
])
</script>

<style scoped lang="scss">
.vgl-layout {
  --vgl-placeholder-bg: red;
  --vgl-placeholder-opacity: 20%;
  --vgl-placeholder-z-index: 2;

  --vgl-item-resizing-z-index: 3;
  --vgl-item-resizing-opacity: 60%;
  --vgl-item-dragging-z-index: 3;
  --vgl-item-dragging-opacity: 100%;

  --vgl-resizer-size: 10px;
  --vgl-resizer-border-color: #444;
  --vgl-resizer-border-width: 2px;
}
.vgl-item--placeholder {
  z-index: var(--vgl-placeholder-z-index, 2);
  user-select: none;
  background-color: var(--vgl-placeholder-bg, red);
  opacity: var(--vgl-placeholder-opacity, 20%);
  transition-duration: 100ms;
}
.vgl-layout {
  --vgl-placeholder-bg: green;
}
// .vgl-layout::before {
//   position: absolute;
//   width: calc(100% - 5px);
//   height: calc(100% - 5px);
//   margin: 5px;
//   content: '';
//   background-image:
//     linear-gradient(to right, lightgrey 1px, transparent 1px),
//     linear-gradient(to bottom, lightgrey 1px, transparent 1px);
//   background-repeat: repeat;
//   background-size: calc(calc(100% - 5px) / 12) 40px;
// }

.vgl-item {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 10px;
}
</style>
