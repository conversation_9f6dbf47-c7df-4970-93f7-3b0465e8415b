<template>
  <div style="height: 50px">
    <vxe-toolbar ref="toolbarRef" custom size="small" class="h-full">
      <template #buttons>
        <el-form inline>
          <el-form-item label="客户更新时间范围">
            <DateRange
              v-model="queryParams.customerTime"
              selected="last3Month"
              @change="onListOpportunity"
            />
          </el-form-item>
          <el-form-item label="销售数据时间范围">
            <DateRange
              v-model="queryParams.salesTime"
              selected="last1Year"
              @change="onListOpportunity"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="onListOpportunity">查询</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #tools>
        <vxe-tooltip content="导出">
          <vxe-button
            icon="vxe-icon-save"
            circle
            :loading="exportLoading"
            @click="handleExport"
            v-hasPermi="['xiaoman:report:export-opportunity']"
          />
        </vxe-tooltip>
      </template>
    </vxe-toolbar>
  </div>
  <div style="height: calc(100vh - 200px - 60px)">
    <vxe-table
      :header-cell-style="{ padding: 0, fontSize: '12px', height: '24px' }"
      :cell-style="{ padding: 0, fontSize: '12px' }"
      :row-config="{ isCurrent: true, isHover: true }"
      :column-config="{ isCurrent: true }"
      :custom-config="customConfig"
      :export-config="{}"
      :data="list"
      :show-footer="true"
      :footer-data="[bottomRow]"
      :footer-cell-style="{
        padding: 0,
        fontSize: '12px',
        fontWeight: 'bold',
        height: '30px',
        backgroundColor: 'var(--el-color-warning-light-8)'
      }"
      :loading="loading || exportLoading"
      :filter-config="{ remote: true }"
      @filter-change="filterMethod"
      @cell-click="(el: any) => tableCellClick(el, tableRef)"
      height="100%"
      border
      align="center"
      stripe
      ref="tableRef"
      id="vxe-table-opportunity"
    >
      <vxe-column
        title="业务员"
        width="100"
        field="salesperson"
        fixed="left"
        :filters="props.userList"
      >
        <template #default="{ row }">
          {{ row.salesperson?.join(',') }}
        </template>
        <template #footer="{ row }">
          {{ row.salesperson?.join(',') }}
        </template>
      </vxe-column>
      <vxe-column
        title="客户所属组别"
        field="departmentName"
        :filters="props.departmentList"
        width="110"
        fixed="left"
      />
      <vxe-column
        title="客户名称"
        field="customerName"
        align="left"
        width="140"
        :key="3"
        fixed="left"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            type="primary"
            size="small"
            link
            v-if="row.customerName"
            @click="openOrderForm(row)"
            :underline="false"
          >
            {{ row.customerName }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="国家/地区"
        field="countryName"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="洲/省"
        field="regionOrProvince"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="城市"
        field="city"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="客户画像"
        field="customerPortraits"
        width="100"
        :filters="props.customerPortrait"
      />
      <vxe-column
        title="客户性质"
        field="nature"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column title="合作阶段" field="trailStatus" width="100" :filters="props.customerStage" />
      <vxe-column
        title="客户官网"
        field="homepage"
        align="left"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            size="small"
            link
            underline
            :href="row.homepage"
            target="_blank"
            v-if="row.homepage"
          >
            {{ row.homepage }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="客户营收规模评估"
        field="revenueScale"
        width="150"
        :filters="props.customerRevenue"
      />
      <vxe-column
        title="香氛产品规模评估"
        field="fragranceRevenueScale"
        width="150"
        :filters="props.customerOilRevenue"
      />
      <vxe-column title="预估年销售额(万元)" field="estimatedAnnualSales" width="100" />
      <vxe-column
        title="竟对机器类数据描述"
        field="untoMachineRemark"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="竟对精油类数据描述"
        field="untoEssentialOilRemark"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="相关性产品描述"
        field="relevantProductRemark"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-colgroup
        title="合作机器类产品TOP5(人民币元)"
        field="machineProducts"
        header-class-name="dynamic_0"
      >
        <template #header>
          合作机器类产品TOP5(人民币元)
          <el-input
            v-model="queryParams.machine"
            size="small"
            placeholder="输入型号回车后进行筛选"
            clearable
            @change="handleList"
            class="!w-200px"
          />
        </template>
        <vxe-colgroup
          :title="`产品-${item}`"
          :field="`machineProduct${item}`"
          v-for="(item, index) in [1, 2, 3, 4, 5]"
          :key="index"
          header-class-name="dynamic_0"
        >
          <vxe-column
            title="型号"
            :field="`machineModel${item}`"
            width="90"
            header-class-name="dynamic_0"
          />
          <vxe-column
            title="金额"
            :field="`machinePrice${item}`"
            width="90"
            :formatter="formatterNumber"
            header-class-name="dynamic_0"
          >
            <template #footer="{ row }">
              {{ formatterNumber(row[`machinePrice${item}`]) }}
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-column
          title="机器合计金额"
          :formatter="formatterNumber"
          field="machineTotalPrice"
          width="90"
          header-class-name="dynamic_0"
        >
          <template #footer="{ row }">
            {{ formatterNumber(row.machineTotalPrice) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup
        title="合作精油类产品TOP5(人民币元)"
        field="oilProducts"
        header-class-name="dynamic_1"
      >
        <template #header>
          合作精油类产品TOP5(人民币元)
          <el-input
            v-model="queryParams.oil"
            size="small"
            placeholder="输入香型回车后进行筛选"
            clearable
            @change="handleList"
            class="!w-200px"
          />
        </template>
        <vxe-colgroup
          :title="`香型-${item}`"
          :field="`oilProduct${item}`"
          v-for="(item, index) in [1, 2, 3, 4, 5]"
          :key="index"
          header-class-name="dynamic_1"
        >
          <vxe-column
            title="香型"
            :field="`oilModel${item}`"
            width="90"
            header-class-name="dynamic_1"
          />
          <vxe-column
            title="金额"
            :field="`oilPrice${item}`"
            width="90"
            :formatter="formatterNumber"
            header-class-name="dynamic_1"
          >
            <template #footer="{ row }">
              {{ formatterNumber(row[`oilPrice${item}`]) }}
            </template>
          </vxe-column>
        </vxe-colgroup>
        <vxe-column
          title="精油合计金额"
          :formatter="formatterNumber"
          field="oilTotalPrice"
          width="90"
          header-class-name="dynamic_1"
        >
          <template #footer="{ row }">
            {{ formatterNumber(row.oilTotalPrice) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
    </vxe-table>
    <!-- <vxe-grid v-bind="gridOptions" /> -->
    <!-- 分页 -->
    <Pagination
      :total="total"
      size="small"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="onListOpportunity"
      class="!float-left"
    />
  </div>
  <OrderForm ref="orderFormRef" />
</template>

<script lang="ts" setup>
import type { VxeToolbarInstance, VxeTableInstance } from 'vxe-table'
import * as ReportApi from '@/api/butt-joint/xiaoman/report'
import { customConfig, tableCellClick } from '@/utils/vxeCustom'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import OrderForm from './OrderForm.vue'
import { propTypes } from '@/utils/propTypes'
import download from '@/utils/download'

const tableRef = ref<VxeTableInstance>()
const toolbarRef = ref<VxeToolbarInstance>()
const orderFormRef = ref()
const exportLoading = ref()

const message = useMessage()

const queryParams = ref({
  pageSize: 30,
  pageNo: 1,
  customerTime: [dayjs().subtract(3, 'months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  salesTime: [dayjs().subtract(1, 'years').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  salesperson: [] as string[],
  departmentName: [] as number[],
  customerName: '',
  countryName: '',
  regionOrProvince: '',
  city: '',
  customerPortraits: [] as string[],
  nature: '',
  homepage: '',
  revenueScale: [] as string[],
  fragranceRevenueScale: [] as string[],
  untoMachineRemark: '',
  untoEssentialOilRemark: '',
  relevantProductRemark: '',
  machine: '',
  oil: ''
})

const props = defineProps({
  userList: propTypes.oneOfType([Array<any>]).isRequired,
  departmentList: propTypes.oneOfType([Array<any>]).isRequired,
  customerPortrait: propTypes.oneOfType([Array<any>]).isRequired,
  customerStage: propTypes.oneOfType([Array<any>]).isRequired,
  customerRevenue: propTypes.oneOfType([Array<any>]).isRequired,
  customerOilRevenue: propTypes.oneOfType([Array<any>]).isRequired
})

const list = ref<any[]>([])
const bottomRow = ref({})
const total = ref(0)
const loading = ref(false)

// const gridOptions = reactive<VxeGridProps<any>>({
//   id: 'vxe-grid-opportunity',
//   toolbarConfig: {
//     custom: true
//   },
//   customConfig: customConfig,
//   rowConfig: {
//     isHover: true,
//     isCurrent: true
//   },
//   border: true,
//   stripe: true,
//   align: 'center',
//   height: '100%',
//   data: list.value,
//   columns: [
//     { title: '业务员', width: 100, field: 'salesperson' },
//     { title: '客户所属组别', width: 100, field: 'departmentName' },
//     {
//       title: '客户信息',
//       field: 'customer',
//       children: [
//         { title: '客户名称', width: 140, field: 'customerName', align: 'left' },
//         { title: '国家/地区', width: 100, field: 'countryName' },
//         { title: '洲/省', width: 100, field: 'regionOrProvince' },
//         { title: '城市', width: 100, field: 'city' }
//       ]
//     },
//     { title: '客户画像', width: 100, field: 'customerPortraits' },
//     { title: '客户性质', width: 100, field: 'nature' },
//     { title: '合作阶段', width: 100, field: 'trailStatus' },
//     { title: '客户官网', width: 200, field: 'homepage' },
//     { title: '客户营收规模评估', width: 150, field: 'revenueScale' },
//     { title: '香氛产品规模评估', width: 150, field: 'fragranceRevenueScale' },
//     { title: '预估年销售额(万元)', width: 100, field: 'estimatedAnnualSales' },
//     { title: '竟对机器类数据描述', width: 200, field: 'untoMachineRemark' },
//     { title: '竟对精油类数据描述', width: 200, field: 'untoEssentialOilRemark' },
//     { title: '相关性产品描述', width: 200, field: 'relevantProductRemark' },
//     {
//       title: '合作机器类产品TOP5(人名币元)',
//       field: 'machineProducts',
//       children: [
//         {
//           title: '产品-1',
//           field: 'machineProduct1',
//           children: [
//             { title: '型号', width: 90, field: 'machineModel1' },
//             { title: '金额', width: 90, field: 'machinePrice1' }
//           ]
//         },
//         {
//           title: '产品-2',
//           field: 'machineProduct2',
//           children: [
//             { title: '型号', width: 90, field: 'machineModel2' },
//             { title: '金额', width: 90, field: 'machinePrice2' }
//           ]
//         },
//         {
//           title: '产品-3',
//           field: 'machineProduct3',
//           children: [
//             { title: '型号', width: 90, field: 'machineModel3' },
//             { title: '金额', width: 90, field: 'machinePrice3' }
//           ]
//         },
//         {
//           title: '产品-4',
//           field: 'machineProduct4',
//           children: [
//             { title: '型号', width: 90, field: 'machineModel4' },
//             { title: '金额', width: 90, field: 'machinePrice4' }
//           ]
//         },
//         {
//           title: '产品-5',
//           field: 'machineProduct5',
//           children: [
//             { title: '型号', width: 90, field: 'machineModel5' },
//             { title: '金额', width: 90, field: 'machinePrice6' }
//           ]
//         }
//       ]
//     },
//     {
//       title: '合作精油类产品TOP5(人名币元)',
//       field: 'oilProducts',
//       children: [
//         {
//           title: '香型-1',
//           field: 'oilProduct1',
//           children: [
//             { title: '香型', width: 90, field: 'oilModel1' },
//             { title: '金额', width: 90, field: 'oilPrice1' }
//           ]
//         },
//         {
//           title: '香型-2',
//           field: 'oilProduct2',
//           children: [
//             { title: '香型', width: 90, field: 'oilModel2' },
//             { title: '金额', width: 90, field: 'oilPrice2' }
//           ]
//         },
//         {
//           title: '香型-3',
//           field: 'oilProduct3',
//           children: [
//             { title: '香型', width: 90, field: 'oilModel3' },
//             { title: '金额', width: 90, field: 'oilPrice3' }
//           ]
//         },
//         {
//           title: '香型-4',
//           field: 'oilProduct4',
//           children: [
//             { title: '香型', width: 90, field: 'oilModel4' },
//             { title: '金额', width: 90, field: 'oilPrice4' }
//           ]
//         },
//         {
//           title: '香型-5',
//           field: 'oilProduct5',
//           children: [
//             { title: '香型', width: 90, field: 'oilModel5' },
//             { title: '金额', width: 90, field: 'oilPrice6' }
//           ]
//         }
//       ]
//     }
//   ]
// })

const handleList = () => {
  queryParams.value.pageNo = 1
  onListOpportunity()
}

const onListOpportunity = async () => {
  loading.value = true
  try {
    const res = await ReportApi.pageOpprotunity(queryParams.value)
    list.value = res.list
    if (res.list && res.list.length > 0) {
      // 取出最后一行的 values 数组
      bottomRow.value = cloneDeep(res.list[res.list.length - 1])
      list.value.pop()
    }
    total.value = res.total
  } finally {
    loading.value = false
  }
}
/** 链接 toolbar 和 table */
const connectToolbarAndTable = () => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
}

const openOrderForm = (row: any) => {
  orderFormRef.value.open(row, 'opportunity')
}

const formatterNumber = (tempValue: number | any) => {
  if (typeof tempValue === 'number') {
    return addThousandSeprator(tempValue.toFixed(0))
  } else if (tempValue && typeof tempValue.cellValue === 'number') {
    return addThousandSeprator(tempValue.cellValue.toFixed(0))
  } else {
    return ''
  }
}

const addThousandSeprator = (strOrNum) => {
  return parseFloat(strOrNum)
    .toString()
    .split('.')
    .map((x, idx) => {
      if (!idx) {
        return x
          .split('')
          .reverse()
          .map((xx, idxx) => (idxx && !(idxx % 3) ? xx + ',' : xx))
          .reverse()
          ?.join('')
      } else {
        return x
      }
    })
    ?.join('.')
}

const textOption = ref([{ data: '' }])
const filterMethod: any = (filter: any) => {
  if (typeof queryParams.value[filter.field] === 'string') {
    queryParams.value[filter.field] = filter.datas.join(',')
  } else {
    queryParams.value[filter.field] = filter.values
  }
  handleList()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportApi.exportOpprotunity(queryParams.value)
    download.excel(data, `小满销售作战机会点明细表${dayjs().unix()}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
onMounted(async () => {
  handleList()
  connectToolbarAndTable()
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.dynamic_0) {
  background-color: var(--el-color-primary-light-8);
}

:deep(.dynamic_1) {
  background-color: var(--el-color-warning-light-8);
}
</style>
