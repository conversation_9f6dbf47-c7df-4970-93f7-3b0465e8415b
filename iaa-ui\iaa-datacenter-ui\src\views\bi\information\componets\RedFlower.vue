<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.sTime"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <el-row>
      <el-col
        v-for="dict in getIntDictOptions('information_person')"
        :key="dict.value"
        :span="6"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        :xl="6"
      >
        <div
          :id="`person-${dict.value}-line`"
          class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
        ></div>
      </el-col>
    </el-row>
    <div class="h-[calc(100%-200px-60px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="日期" field="dateTime">
          <template #default="{ row }">
            {{ formatToDateTime(row.dateTime) }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { getIntDictOptions } from '@/utils/dict'
import { formatToDateTime } from '@/utils/dateUtil'
import { RedApi } from '@/api/butt-joint/red'

const queryParams = reactive({
  dateType: 'year' as any,
  sTime: moment().format('YYYY-MM-DD')
})
const loading = ref(false)
const dataList = ref<any[]>([])

const onList = async () => {
  loading.value = true
  try {
    const res = await RedApi.getFlowerList(queryParams)
    dataList.value = res
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  onList()
})
</script>
