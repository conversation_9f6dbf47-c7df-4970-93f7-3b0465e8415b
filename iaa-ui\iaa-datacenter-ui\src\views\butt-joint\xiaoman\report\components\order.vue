<template>
  <div style="height: 50px">
    <vxe-toolbar ref="toolbarRef" custom size="small" class="h-full">
      <template #buttons>
        <el-form inline>
          <el-form-item label="时间范围">
            <DateRange
              v-model="queryParams.salesTime"
              type="month"
              selected="after3Month"
              :hide-radio="['today', 'yesterday', 'thisWeek', 'lastWeek']"
            />
          </el-form-item>
          <el-form-item>
            <el-checkbox
              v-model="queryParams.viewType"
              label="仅查看有商机的数据"
              :true-value="1"
              :false-value="0"
              @change="onListOrder"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="onListOrder">查询</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #tools>
        <vxe-tooltip content="导出">
          <vxe-button
            icon="vxe-icon-save"
            circle
            :loading="exportLoading"
            @click="handleExport"
            v-hasPermi="['xiaoman:report:export-order']"
          />
        </vxe-tooltip>
      </template>
    </vxe-toolbar>
  </div>
  <div style="height: calc(100vh - 200px - 60px)">
    <vxe-table
      :header-cell-style="{ padding: 0, fontSize: '12px', height: '24px' }"
      :cell-style="{ padding: 0, fontSize: '12px' }"
      :footer-cell-style="{
        padding: 0,
        fontSize: '12px',
        height: '30px',
        fontWeight: 'bold',
        backgroundColor: 'var(--el-color-warning-light-8)'
      }"
      :row-config="{ isCurrent: true, isHover: true }"
      :column-config="{ isCurrent: true }"
      :custom-config="customConfig"
      :data="list"
      :loading="loading || exportLoading"
      :show-footer="true"
      :footer-data="[bottomRow]"
      :export-config="{}"
      :filter-config="{ remote: true }"
      @filter-change="filterMethod"
      @cell-click="(el: any) => tableCellClick(el, tableRef)"
      height="100%"
      border
      align="center"
      stripe
      ref="tableRef"
      id="vxe-table-order"
    >
      <vxe-column
        title="业务员"
        width="100"
        field="salesperson"
        fixed="left"
        :filters="props.userList"
      >
        <template #default="{ row }">
          {{ row.salesperson?.join(',') }}
        </template>
      </vxe-column>
      <vxe-column
        title="客户所属组别"
        field="departmentName"
        :filters="props.departmentList"
        width="110"
        fixed="left"
      />
      <vxe-column
        title="客户名称"
        field="customerName"
        align="left"
        width="140"
        :key="3"
        fixed="left"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            type="primary"
            size="small"
            link
            v-if="row.customerName"
            @click="openOrderForm(row)"
            :underline="false"
          >
            {{ row.customerName }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="国家/地区"
        field="countryName"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="洲/省"
        field="regionOrProvince"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="城市"
        field="city"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="客户画像"
        field="customerPortraits"
        width="100"
        :filters="props.customerPortrait"
      />
      <vxe-column
        title="客户性质"
        field="nature"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column title="合作阶段" field="trailStatus" width="100" :filters="props.customerStage" />
      <vxe-column
        title="客户官网"
        field="homepage"
        align="left"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            size="small"
            link
            underline
            :href="row.homepage"
            target="_blank"
            v-if="row.homepage"
          >
            {{ row.homepage }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="客户营收规模评估"
        field="revenueScale"
        width="150"
        :filters="props.customerRevenue"
      />
      <vxe-column
        title="香氛产品规模评估"
        field="fragranceRevenueScale"
        width="150"
        :filters="props.customerOilRevenue"
      />
      <vxe-column title="预估年销售额(万元)" field="estimatedAnnualSales" width="100" />
      <vxe-colgroup title="近三年销售额" field="last3YearsSalesGroup" :key="14">
        <vxe-column
          width="100"
          :title="dayjs().subtract(2, 'years').format('YYYY')"
          filed="last2YearsSales"
          :key="15"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().subtract(2, 'years').format('YYYY')]) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().subtract(2, 'years').format('YYYY')]) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          :title="dayjs().subtract(1, 'years').format('YYYY')"
          filed="last1YearsSales"
          :key="16"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().subtract(1, 'years').format('YYYY')]) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().subtract(1, 'years').format('YYYY')]) }}
          </template>
        </vxe-column>
        <vxe-column width="100" :title="dayjs().format('YYYY')" filed="last0YearsSales" :key="17">
          <template #default="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().format('YYYY')]) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.yearOrderMap?.[dayjs().format('YYYY')]) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-column width="100" title="竟对情况" field="corrival" :key="18" />
      <!-- <vxe-column width="100" title="单价" field="unitPrice" :key="19" /> -->
      <vxe-column
        width="100"
        title="预测2024年销售额"
        :formatter="formatterNumber"
        field="tztfourPredictionSales"
        :key="20"
      >
        <template #footer="{ row }">
          {{ formatterNumber(row?.tztfourPredictionSales) }}
        </template>
      </vxe-column>
      <vxe-column
        width="100"
        title="2024达标情况"
        :formatter="formatterNumber"
        field="tztfourReachingStandard"
        :key="21"
      >
        <template #footer="{ row }">
          {{ formatterNumber(row?.tztfourReachingStandard) }}
        </template>
      </vxe-column>
      <vxe-column
        width="100"
        title="预测2025年销售额"
        :formatter="formatterNumber"
        field="tztfivePredictionSales"
        :key="22"
      >
        <template #footer="{ row }">
          {{ formatterNumber(row?.tztfivePredictionSales) }}
        </template>
      </vxe-column>
      <vxe-column
        width="100"
        title="2025达标情况"
        :formatter="formatterNumber"
        field="tztfiveReachingStandard"
        :key="23"
      >
        <template #footer="{ row }">
          {{ formatterNumber(row?.tztfiveReachingStandard) }}
        </template>
      </vxe-column>
      <vxe-colgroup
        v-for="(item, index) in salesColumn"
        :title="`${item.title}销售数据`"
        :field="`dynamic_${item.field}`"
        :key="24 + index"
        :header-class-name="`dynamic_${index % 2}`"
      >
        <vxe-column
          width="100"
          title="预测销量"
          :field="`dynamic_order_${item.field}_forecastQty`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ row?.orderTotalList?.[item.title]?.forecastQty }}
          </template>
          <template #footer="{ row }">
            {{ row?.orderTotalList?.[item.title]?.forecastQty }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="预测金额"
          :field="`dynamic_order_${item.field}_forecastAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.forecastAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.forecastAmount) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="第一周"
          :field="`dynamic_order_${item.field}_oneWeekAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.oneWeekAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.oneWeekAmount) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="第二周"
          :field="`dynamic_order_${item.field}_twoWeekAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.twoWeekAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.twoWeekAmount) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="第三周"
          :field="`dynamic_order_${item.field}_threeWeekAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.threeWeekAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.threeWeekAmount) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="第四周"
          :field="`dynamic_order_${item.field}_fourWeekAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.fourWeekAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.fourWeekAmount) }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="总销量"
          :field="`dynamic_order_${item.field}_totalQty`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ row?.orderTotalList?.[item.title]?.totalQty }}
          </template>
          <template #footer="{ row }">
            {{ row?.orderTotalList?.[item.title]?.totalQty }}
          </template>
        </vxe-column>
        <vxe-column
          width="100"
          title="总销售额"
          :field="`dynamic_order_${item.field}_totalAmount`"
          :header-class-name="`dynamic_${index % 2}`"
        >
          <template #default="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.totalAmount) }}
          </template>
          <template #footer="{ row }">
            {{ formatterNumber(row?.orderTotalList?.[item.title]?.totalAmount) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-column
        v-for="(item, index) in salesColumn"
        width="100"
        :title="`${item.title}回款`"
        :field="`dynamic_payment_${item.field}_return`"
        :key="24 + salesColumn.length + index"
        :header-class-name="`dynamic_${index % 2}`"
      >
        <template #default="{ row }">
          {{ formatterNumber(row?.paymentMap?.[item.title]) }}
        </template>
        <template #footer="{ row }">
          {{ formatterNumber(row?.paymentMap?.[item.title]) }}
        </template>
      </vxe-column>
    </vxe-table>
    <!-- <vxe-grid v-bind="gridOptions" /> -->
    <!-- 分页 -->
    <Pagination
      :total="total"
      size="small"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="onListOrder"
      class="!float-left"
    />
  </div>
  <OrderForm ref="orderFormRef" />
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { tableCellClick, customConfig } from '@/utils/vxeCustom'
import * as ReportApi from '@/api/butt-joint/xiaoman/report'
import OrderForm from './OrderForm.vue'
import { cloneDeep } from 'lodash-es'
import { propTypes } from '@/utils/propTypes'
import download from '@/utils/download'

const tableRef = ref()
const toolbarRef = ref()
const orderFormRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  salesTime: [dayjs().format('YYYY-MM-DD'), dayjs().add(3, 'months').format('YYYY-MM-DD')],
  viewType: 0,
  salesperson: [] as string[],
  departmentName: [] as number[],
  customerName: '',
  countryName: '',
  regionOrProvince: '',
  city: '',
  customerPortraits: [] as string[],
  nature: '',
  homepage: '',
  revenueScale: [] as string[],
  fragranceRevenueScale: [] as string[]
})

const props = defineProps({
  userList: propTypes.oneOfType([Array<any>]).isRequired,
  departmentList: propTypes.oneOfType([Array<any>]).isRequired,
  customerPortrait: propTypes.oneOfType([Array<any>]).isRequired,
  customerStage: propTypes.oneOfType([Array<any>]).isRequired,
  customerRevenue: propTypes.oneOfType([Array<any>]).isRequired,
  customerOilRevenue: propTypes.oneOfType([Array<any>]).isRequired
})

const list = ref<any[]>([])
const bottomRow = ref<any>()
const loading = ref(false)
const total = ref(0)
// 销售表格列
const salesColumn = computed(() => {
  const startDate = dayjs(queryParams.value.salesTime[0])
  const endDate = dayjs(queryParams.value.salesTime[1])
  let monthDifference = endDate.diff(startDate, 'months')
  const columns = [] as any[]
  for (let i = 0; i <= monthDifference; i++) {
    columns.push({
      title: startDate.add(i, 'months').format('YYYY-MM'),
      field: `sales.${startDate.add(i, 'months').format('YYYY-MM')}`
    })
  }
  return columns
})

const handleList = () => {
  queryParams.value.pageNo = 1
  onListOrder()
}

const onListOrder = async () => {
  loading.value = true
  try {
    const res = await ReportApi.pageOrder(queryParams.value)
    list.value = res.list
    if (res.list && res.list.length > 0) {
      // 取出最后一行的 values 数组
      bottomRow.value = cloneDeep(res.list[res.list.length - 1])
      list.value.pop()
    }
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 链接 toolbar 和 table */
const connectToolbarAndTable = () => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
}

const formatterNumber = (tempValue: number | any) => {
  if (typeof tempValue === 'number') {
    return addThousandSeprator(tempValue.toFixed(0))
  } else if (tempValue && typeof tempValue.cellValue === 'number') {
    return addThousandSeprator(tempValue.cellValue.toFixed(0))
  } else {
    return ''
  }
}

const addThousandSeprator = (strOrNum) => {
  return parseFloat(strOrNum)
    .toString()
    .split('.')
    .map((x, idx) => {
      if (!idx) {
        return x
          .split('')
          .reverse()
          .map((xx, idxx) => (idxx && !(idxx % 3) ? xx + ',' : xx))
          .reverse()
          .join('')
      } else {
        return x
      }
    })
    .join('.')
}

const openOrderForm = (row: any) => {
  orderFormRef.value.open(row, 'order')
}

const textOption = ref([{ data: '' }])
const filterMethod: any = (filter: any) => {
  if (typeof queryParams.value[filter.field] === 'string') {
    queryParams.value[filter.field] = filter.datas.join(',')
  } else {
    queryParams.value[filter.field] = filter.values
  }
  handleList()
}

const exportLoading = ref()

const message = useMessage()

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportApi.exportOrder(queryParams.value)
    download.excel(data, `小满销售作战滚动订单明细表${dayjs().unix()}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onListOrder()
  connectToolbarAndTable()
})
</script>

<style lang="scss" scoped>
:deep(.dynamic_0) {
  background-color: var(--el-color-primary-light-8);
}

:deep(.dynamic_1) {
  background-color: var(--el-color-warning-light-8);
}
</style>
