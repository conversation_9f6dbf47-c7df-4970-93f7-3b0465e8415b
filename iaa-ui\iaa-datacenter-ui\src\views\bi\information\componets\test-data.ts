// 测试数据生成器 - 用于验证流程及时交付率功能

import moment from 'moment'

// 模拟流程数据接口
interface MockFlowData {
  flowId: string
  flowTemplate: string
  flowNumber: string
  flowName: string
  flowCreator: string
  flowCreateTime: string
  flowPlanDate: string
  flowType: number[]
  flowPublishTime: string | null
  flowStatus: string
}

// 生成测试数据
export const generateMockFlowData = (count: number = 50): MockFlowData[] => {
  const data: MockFlowData[] = []
  const templates = ['采购流程', '审批流程', '项目流程', '财务流程', '人事流程']
  const creators = ['张三', '李四', '王五', '赵六']
  const persons = [1, 2, 3, 4] // 对应字典中的负责人ID
  
  for (let i = 0; i < count; i++) {
    const createTime = moment().subtract(Math.floor(Math.random() * 180), 'days')
    const planDate = createTime.clone().add(Math.floor(Math.random() * 30) + 1, 'days')
    
    // 随机决定是否已完成
    const isCompleted = Math.random() > 0.3
    let publishTime: string | null = null
    
    if (isCompleted) {
      // 已完成的流程，随机设置完成时间
      const completeDays = Math.floor(Math.random() * 40) - 5 // -5到35天的范围
      publishTime = planDate.clone().add(completeDays, 'days').format('YYYY-MM-DD HH:mm:ss')
    }
    
    // 随机分配负责人（可能多个）
    const assignedPersons: number[] = []
    const personCount = Math.floor(Math.random() * 3) + 1 // 1-3个负责人
    
    for (let j = 0; j < personCount; j++) {
      const personId = persons[Math.floor(Math.random() * persons.length)]
      if (!assignedPersons.includes(personId)) {
        assignedPersons.push(personId)
      }
    }
    
    data.push({
      flowId: `FLOW-${String(i + 1).padStart(4, '0')}`,
      flowTemplate: templates[Math.floor(Math.random() * templates.length)],
      flowNumber: `FN${String(i + 1).padStart(6, '0')}`,
      flowName: `${templates[Math.floor(Math.random() * templates.length)]} - ${i + 1}`,
      flowCreator: creators[Math.floor(Math.random() * creators.length)],
      flowCreateTime: createTime.format('YYYY-MM-DD HH:mm:ss'),
      flowPlanDate: planDate.format('YYYY-MM-DD'),
      flowType: assignedPersons,
      flowPublishTime: publishTime,
      flowStatus: isCompleted ? '已完成' : '进行中'
    })
  }
  
  return data.sort((a, b) => moment(b.flowCreateTime).diff(moment(a.flowCreateTime)))
}

// 生成特定场景的测试数据
export const generateScenarioData = () => {
  const scenarios: MockFlowData[] = []
  const baseDate = moment('2024-01-01')
  
  // 场景1：正常完成的流程
  scenarios.push({
    flowId: 'FLOW-NORMAL-001',
    flowTemplate: '测试流程',
    flowNumber: 'TEST001',
    flowName: '正常完成测试流程',
    flowCreator: '测试用户',
    flowCreateTime: baseDate.format('YYYY-MM-DD HH:mm:ss'),
    flowPlanDate: baseDate.clone().add(10, 'days').format('YYYY-MM-DD'),
    flowType: [1],
    flowPublishTime: baseDate.clone().add(9, 'days').format('YYYY-MM-DD HH:mm:ss'), // 提前1天完成
    flowStatus: '已完成'
  })
  
  // 场景2：延期完成的流程
  scenarios.push({
    flowId: 'FLOW-DELAYED-001',
    flowTemplate: '测试流程',
    flowNumber: 'TEST002',
    flowName: '延期完成测试流程',
    flowCreator: '测试用户',
    flowCreateTime: baseDate.format('YYYY-MM-DD HH:mm:ss'),
    flowPlanDate: baseDate.clone().add(10, 'days').format('YYYY-MM-DD'),
    flowType: [2],
    flowPublishTime: baseDate.clone().add(15, 'days').format('YYYY-MM-DD HH:mm:ss'), // 延期5天完成
    flowStatus: '已完成'
  })
  
  // 场景3：未完成的流程
  scenarios.push({
    flowId: 'FLOW-UNFINISHED-001',
    flowTemplate: '测试流程',
    flowNumber: 'TEST003',
    flowName: '未完成测试流程',
    flowCreator: '测试用户',
    flowCreateTime: baseDate.format('YYYY-MM-DD HH:mm:ss'),
    flowPlanDate: baseDate.clone().add(10, 'days').format('YYYY-MM-DD'),
    flowType: [3],
    flowPublishTime: null, // 未完成
    flowStatus: '进行中'
  })
  
  // 场景4：多负责人流程
  scenarios.push({
    flowId: 'FLOW-MULTI-001',
    flowTemplate: '测试流程',
    flowNumber: 'TEST004',
    flowName: '多负责人测试流程',
    flowCreator: '测试用户',
    flowCreateTime: baseDate.format('YYYY-MM-DD HH:mm:ss'),
    flowPlanDate: baseDate.clone().add(10, 'days').format('YYYY-MM-DD'),
    flowType: [1, 2, 3], // 多个负责人
    flowPublishTime: baseDate.clone().add(11, 'days').format('YYYY-MM-DD HH:mm:ss'), // 延期1天完成
    flowStatus: '已完成'
  })
  
  return scenarios
}

// 计算预期的统计结果
export const calculateExpectedStats = (data: MockFlowData[]) => {
  const stats: Record<string, Record<string, { normal: number; delayed: number; unfinished: number }>> = {}
  
  data.forEach(flow => {
    if (!flow.flowType || !Array.isArray(flow.flowType)) return
    
    const flowMonth = moment(flow.flowCreateTime).format('YYYY-MM')
    let status: string
    
    if (!flow.flowPublishTime) {
      status = 'unfinished'
    } else {
      const planDate = moment(flow.flowPlanDate)
      const publishDate = moment(flow.flowPublishTime)
      const daysDiff = publishDate.diff(planDate, 'days')
      status = daysDiff <= 1 ? 'normal' : 'delayed'
    }
    
    flow.flowType.forEach(personId => {
      const personKey = personId.toString()
      if (!stats[personKey]) {
        stats[personKey] = {}
      }
      if (!stats[personKey][flowMonth]) {
        stats[personKey][flowMonth] = { normal: 0, delayed: 0, unfinished: 0 }
      }
      
      if (status === 'normal') {
        stats[personKey][flowMonth].normal++
      } else if (status === 'delayed') {
        stats[personKey][flowMonth].delayed++
      } else {
        stats[personKey][flowMonth].unfinished++
      }
    })
  })
  
  return stats
}

// 验证函数
export const validateFlowStatusCalculation = () => {
  const testCases = [
    {
      planDate: '2024-01-10',
      publishTime: '2024-01-09 10:00:00',
      expected: '正常',
      description: '提前完成'
    },
    {
      planDate: '2024-01-10',
      publishTime: '2024-01-10 10:00:00',
      expected: '正常',
      description: '按时完成'
    },
    {
      planDate: '2024-01-10',
      publishTime: '2024-01-11 10:00:00',
      expected: '正常',
      description: '延期1天内'
    },
    {
      planDate: '2024-01-10',
      publishTime: '2024-01-12 10:00:00',
      expected: '延期',
      description: '延期超过1天'
    },
    {
      planDate: '2024-01-10',
      publishTime: null,
      expected: '未结束',
      description: '未完成'
    }
  ]
  
  console.log('流程状态计算验证:')
  testCases.forEach(testCase => {
    // 这里应该调用实际的计算函数进行验证
    console.log(`${testCase.description}: 期望 ${testCase.expected}`)
  })
}

// 导出默认测试数据
export default {
  generateMockFlowData,
  generateScenarioData,
  calculateExpectedStats,
  validateFlowStatusCalculation
}
