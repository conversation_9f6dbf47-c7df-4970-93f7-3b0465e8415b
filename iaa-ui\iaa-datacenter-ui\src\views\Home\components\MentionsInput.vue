<template>
  <div class="mentions-input-container relative">
    <div
      class="relative"
      :class="{ 'has-value': content.length > 0 }"
    >
      <!-- 文本区域 -->
      <div
        ref="inputRef"
        class="w-full min-h-[100px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all outline-none"
        :contenteditable="!disabled"
        :placeholder="placeholder"
        @input="handleInput"
        @keydown="handleKeydown"
        @click="handleClick"
        @paste="handlePaste"
        @blur="handleBlur"
      ></div>
      
      <!-- 浮动弹出框 -->
      <div
        v-if="isDropdownVisible"
        ref="dropdownRef"
        :style="dropdownStyle"
        class="z-10 bg-white rounded-lg shadow-lg border border-gray-200 max-h-60 overflow-y-auto min-w-[200px]"
      >
        <ul class="divide-y divide-gray-100">
          <li
            v-for="(user, index) in filteredUsers"
            :key="user.id"
            class="px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{ 'bg-primary/10': index === selectedUserIndex }"
            @click="selectUser(user)"
          >
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                {{ user.name.charAt(0).toUpperCase() }}
              </div>
              <div class="ml-3">
                <div class="font-medium text-gray-900">{{ user.name }}</div>
                <div class="text-xs text-gray-500">{{ user.email }}</div>
              </div>
            </div>
          </li>
        </ul>
        <div v-if="filteredUsers.length === 0 && searchQuery.length > 0" class="px-4 py-3 text-sm text-gray-500">
          没有找到 "{{ searchQuery }}" 相关的用户
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';

// 定义用户类型
interface User {
  id: string | number;
  name: string;
  email: string;
}

// 定义props
const props = defineProps<{
  users: User[];
  triggerChar?: string;
  maxLength?: number;
  placeholder?: string;
  disabled?: boolean;
}>();

// 定义emits
const emits = defineEmits<{
  (e: 'input', value: string): void;
  (e: 'mention-select', user: User): void;
  (e: 'blur'): void;
}>();

// 内部状态
const inputRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const content = ref('');
const isDropdownVisible = ref(false);
const filteredUsers = ref<User[]>([]);
const searchQuery = ref('');
const selectedUserIndex = ref(-1);
const mentionStartIndex = ref(-1);
const triggerChar = computed(() => props.triggerChar || '@');
const maxLength = computed(() => props.maxLength || Infinity);
const dropdownStyle = ref({});

// 初始化
onMounted(() => {
  updateContent();
  document.addEventListener('click', handleDocumentClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick);
});

// 处理文档点击事件
const handleDocumentClick = (event: MouseEvent) => {
  if (!inputRef.value || !dropdownRef.value) return;
  
  // 如果点击不在输入框和下拉框内，则隐藏下拉框
  if (!inputRef.value.contains(event.target as Node) && !dropdownRef.value.contains(event.target as Node)) {
    isDropdownVisible.value = false;
  }
};

// 监听props.users变化
watch(() => props.users, () => {
  filterUsers();
});

// 处理输入事件
const handleInput = () => {
  if (!inputRef.value) return;
  
  // 检查是否超过最大长度
  if (getContentLength() > maxLength.value) {
    truncateContent();
    return;
  }
  
  updateContent();
  checkMentionTrigger();
};

// 处理按键事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!inputRef.value) return;
  
  // 处理上下箭头键
  if (isDropdownVisible.value) {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      selectedUserIndex.value = (selectedUserIndex.value + 1) % filteredUsers.value.length;
      return;
    }
    
    if (event.key === 'ArrowUp') {
      event.preventDefault();
      selectedUserIndex.value = (selectedUserIndex.value - 1 + filteredUsers.value.length) % filteredUsers.value.length;
      return;
    }
    
    // 处理回车键
    if (event.key === 'Enter' && selectedUserIndex.value >= 0) {
      event.preventDefault();
      selectUser(filteredUsers.value[selectedUserIndex.value]);
      return;
    }
    
    // 处理Escape键
    if (event.key === 'Escape') {
      isDropdownVisible.value = false;
      return;
    }
  }
  
  // 允许回车换行
  if (event.key === 'Enter') {
    // 检查是否会超过最大长度
    if (getContentLength() + 1 > maxLength.value) {
      event.preventDefault();
      return;
    }
    
    // 插入换行
    document.execCommand('insertLineBreak');
    event.preventDefault();
    
    nextTick(() => {
      updateContent();
      checkMentionTrigger();
    });
  }
  
  // 处理删除键
  if (event.key === 'Backspace') {
    nextTick(() => {
      updateContent();
      checkMentionTrigger();
    });
  }
};

// 处理点击事件
const handleClick = () => {
  checkMentionTrigger();
};

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault();
  
  if (!inputRef.value) return;
  
  const text = (event.clipboardData || window.clipboardData)?.getData('text') || '';
  
  // 检查粘贴后的长度是否超过最大限制
  if (getContentLength() + text.length > maxLength.value) {
    return;
  }
  
  // 插入文本
  document.execCommand('insertText', false, text);
  
  nextTick(() => {
    updateContent();
    checkMentionTrigger();
  });
};

// 处理失焦事件
const handleBlur = () => {
  // 使用setTimeout确保点击选择用户的事件能被触发
  setTimeout(() => {
    isDropdownVisible.value = false;
    emits('blur');
  }, 150);
};

// 更新内容
const updateContent = () => {
  if (!inputRef.value) return;
  
  // 提取纯文本内容
  content.value = inputRef.value.innerText;
  
  // 触发input事件
  emits('input', content.value);
};

// 检查是否触发了提及
const checkMentionTrigger = () => {
  if (!inputRef.value) return;
  
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    isDropdownVisible.value = false;
    return;
  }
  
  const range = selection.getRangeAt(0);
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(inputRef.value);
  preCaretRange.setEnd(range.startContainer, range.startOffset);
  
  // 获取光标前的文本
  const preCaretText = preCaretRange.toString();
  
  // 查找最后一个触发字符
  const lastTriggerIndex = preCaretText.lastIndexOf(triggerChar.value);
  
  if (lastTriggerIndex !== -1) {
    // 检查触发字符后是否有文本
    const afterTriggerText = preCaretText.substring(lastTriggerIndex + 1);
    
    // 如果触发字符后有文本，显示下拉框
    if (afterTriggerText.trim().length > 0) {
      searchQuery.value = afterTriggerText;
      mentionStartIndex.value = lastTriggerIndex;
      filterUsers();
      isDropdownVisible.value = true;
      selectedUserIndex.value = 0;
      
      // 计算下拉框位置
      updateDropdownPosition(range);
      return;
    }
  }
  
  isDropdownVisible.value = false;
};

// 更新下拉框位置
const updateDropdownPosition = (range: Range) => {
  if (!inputRef.value || !dropdownRef.value) return;
  
  // 获取光标的边界矩形
  const rect = range.getBoundingClientRect();
  const inputRect = inputRef.value.getBoundingClientRect();
  
  // 计算下拉框的位置（相对于输入框）
  const top = rect.bottom - inputRect.top;
  const left = rect.left - inputRect.left;
  
  // 设置下拉框样式
  dropdownStyle.value = {
    position: 'absolute',
    top: `${top}px`,
    left: `${left}px`,
    maxWidth: `${inputRect.width - left}px`
  };
};

// 过滤用户
const filterUsers = () => {
  if (!searchQuery.value) {
    filteredUsers.value = props.users;
    return;
  }
  
  const query = searchQuery.value.toLowerCase();
  filteredUsers.value = props.users.filter(user => 
    user.name.toLowerCase().includes(query) || 
    user.email.toLowerCase().includes(query)
  );
};

// 选择用户
const selectUser = (user: User) => {
  if (!inputRef.value) return;
  
  // 创建提及标签
  const mentionSpan = document.createElement('span');
  mentionSpan.className = 'mention bg-primary/10 text-primary px-2 py-1 rounded-md whitespace-nowrap';
  mentionSpan.setAttribute('data-user-id', user.id.toString());
  mentionSpan.setAttribute('contenteditable', 'false');
  mentionSpan.innerHTML = `${triggerChar.value}${user.name} `;
  
  // 删除当前查询文本
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return;
  
  const range = selection.getRangeAt(0);
  range.setStart(range.startContainer, mentionStartIndex.value);
  range.setEnd(range.startContainer, mentionStartIndex.value + searchQuery.value.length + triggerChar.value.length);
  range.deleteContents();
  
  // 插入提及标签
  range.insertNode(mentionSpan);
  
  // 将光标移动到提及标签后
  const newRange = document.createRange();
  newRange.setStartAfter(mentionSpan);
  newRange.collapse(true);
  
  selection.removeAllRanges();
  selection.addRange(newRange);
  
  // 更新内容并隐藏下拉框
  isDropdownVisible.value = false;
  nextTick(() => {
    updateContent();
    
    // 触发mention-select事件
    emits('mention-select', user);
  });
};

// 获取内容长度
const getContentLength = () => {
  if (!inputRef.value) return 0;
  return inputRef.value.innerText.length;
};

// 截断内容以符合最大长度限制
const truncateContent = () => {
  if (!inputRef.value) return;
  
  let length = 0;
  
  // 遍历所有子节点，直到达到最大长度
  for (const node of Array.from(inputRef.value.childNodes)) {
    if (length >= maxLength.value) {
      inputRef.value.removeChild(node);
      continue;
    }
    
    if (node.nodeType === Node.TEXT_NODE) {
      const nodeText = node.textContent || '';
      const remainingLength = maxLength.value - length;
      
      if (nodeText.length > remainingLength) {
        node.textContent = nodeText.substring(0, remainingLength);
        length = maxLength.value;
      } else {
        length += nodeText.length;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 对于提及标签，整个保留或不保留
      const nodeLength = (node.textContent || '').length;
      
      if (length + nodeLength <= maxLength.value) {
        length += nodeLength;
      } else {
        inputRef.value.removeChild(node);
      }
    }
  }
  
  updateContent();
};

// 监听点击事件，处理提及标签的点击
onMounted(() => {
  if (!inputRef.value) return;
  
  inputRef.value.addEventListener('click', (event: Event) => {
    const target = event.target as HTMLElement;
    
    if (target.classList.contains('mention')) {
      const userId = target.getAttribute('data-user-id');
      
      if (userId) {
        const user = props.users.find(u => u.id.toString() === userId);
        if (user) {
          emits('mention-select', user);
        }
      }
    }
  });
});
</script>

<style scoped>
.mentions-input-container {
  font-family: 'Inter', system-ui, sans-serif;
}

.mentions-input-container .has-value::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 10px;
  width: 8px;
  height: 8px;
  background-color: #4ade80;
  border-radius: 50%;
}

.mentions-input-container [contenteditable]:empty::before {
  content: attr(placeholder);
  color: #94a3b8;
  pointer-events: none;
}

.mention {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mention:hover {
  background-color: #3b82f6/20;
}
</style>    