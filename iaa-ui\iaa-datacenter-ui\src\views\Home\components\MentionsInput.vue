<template>
  <div class="mentions-input-container relative">
    <div
      class="relative"
      :class="{ 'has-value': content.length > 0 }"
    >
      <!-- 文本区域 -->
      <div
        ref="inputRef"
        class="w-full min-h-[100px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all outline-none"
        :contenteditable="!disabled"
        :placeholder="placeholder"
        @input="handleInput"
        @keydown="handleKeydown"
        @click="handleClick"
        @paste="handlePaste"
        @blur="handleBlur"
      ></div>

      <!-- 浮动弹出框 -->
      <div
        v-if="isDropdownVisible"
        ref="dropdownRef"
        :style="dropdownStyle"
        class="z-10 bg-white rounded-lg shadow-lg border border-gray-200 max-h-60 overflow-y-auto min-w-[200px]"
      >
        <ul class="divide-y divide-gray-100">
          <li
            v-for="(user, index) in filteredUsers"
            :key="user.id"
            class="px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{ 'bg-primary/10': index === selectedUserIndex }"
            @click="selectUser(user)"
          >
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                {{ user.name.charAt(0).toUpperCase() }}
              </div>
              <div class="ml-3">
                <div class="font-medium text-gray-900">{{ user.name }}</div>
                <div class="text-xs text-gray-500">{{ user.email }}</div>
              </div>
            </div>
          </li>
        </ul>
        <div v-if="filteredUsers.length === 0 && searchQuery.length > 0" class="px-4 py-3 text-sm text-gray-500">
          没有找到 "{{ searchQuery }}" 相关的用户
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue';

// 定义用户类型
interface User {
  id: string | number;
  name: string;
  email: string;
}

// 定义props
const props = defineProps<{
  users: User[];
  modelValue?: string;
  triggerChar?: string;
  maxLength?: number;
  placeholder?: string;
  disabled?: boolean;
}>();

// 定义emits
const emits = defineEmits<{
  (e: 'input', value: string): void;
  (e: 'update:modelValue', value: string): void;
  (e: 'mention-select', user: User): void;
  (e: 'blur'): void;
}>();

// 内部状态
const inputRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const content = ref('');
const isDropdownVisible = ref(false);
const filteredUsers = ref<User[]>([]);
const searchQuery = ref('');
const selectedUserIndex = ref(-1);
const mentionStartIndex = ref(-1);
const triggerChar = computed(() => props.triggerChar || '@');
const maxLength = computed(() => props.maxLength || Infinity);
const dropdownStyle = ref({});

// 初始化
onMounted(() => {
  // 如果有初始值，设置到输入框
  if (props.modelValue && inputRef.value) {
    inputRef.value.innerHTML = props.modelValue;
    content.value = props.modelValue;
  }
  updateContent();
  document.addEventListener('click', handleDocumentClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick);
});

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value && inputRef.value) {
    inputRef.value.innerHTML = newValue || '';
    content.value = newValue || '';
  }
});

// 处理文档点击事件
const handleDocumentClick = (event: MouseEvent) => {
  if (!inputRef.value || !dropdownRef.value) return;

  // 如果点击不在输入框和下拉框内，则隐藏下拉框
  if (!inputRef.value.contains(event.target as Node) && !dropdownRef.value.contains(event.target as Node)) {
    isDropdownVisible.value = false;
  }
};

// 监听props.users变化
watch(() => props.users, () => {
  filterUsers();
});

// 处理输入事件
const handleInput = () => {
  if (!inputRef.value) return;

  // 检查是否超过最大长度
  if (getContentLength() > maxLength.value) {
    truncateContent();
    return;
  }

  updateContent();
  checkMentionTrigger();
};

// 处理按键事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!inputRef.value) return;

  // 处理上下箭头键
  if (isDropdownVisible.value) {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      selectedUserIndex.value = (selectedUserIndex.value + 1) % filteredUsers.value.length;
      return;
    }

    if (event.key === 'ArrowUp') {
      event.preventDefault();
      selectedUserIndex.value = (selectedUserIndex.value - 1 + filteredUsers.value.length) % filteredUsers.value.length;
      return;
    }

    // 处理回车键
    if (event.key === 'Enter' && selectedUserIndex.value >= 0) {
      event.preventDefault();
      selectUser(filteredUsers.value[selectedUserIndex.value]);
      return;
    }

    // 处理Escape键
    if (event.key === 'Escape') {
      isDropdownVisible.value = false;
      return;
    }
  }

  // 允许回车换行
  if (event.key === 'Enter') {
    // 检查是否会超过最大长度
    if (getContentLength() + 1 > maxLength.value) {
      event.preventDefault();
      return;
    }

    // 插入换行
    document.execCommand('insertLineBreak');
    event.preventDefault();

    nextTick(() => {
      updateContent();
      checkMentionTrigger();
    });
  }

  // 处理删除键
  if (event.key === 'Backspace') {
    // 检查是否要删除mention标签
    if (handleMentionDeletion(event)) {
      return;
    }

    nextTick(() => {
      updateContent();
      checkMentionTrigger();
    });
  }
};

// 处理点击事件
const handleClick = () => {
  checkMentionTrigger();
};

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault();

  if (!inputRef.value) return;

  const text = (event.clipboardData || window.clipboardData)?.getData('text') || '';

  // 检查粘贴后的长度是否超过最大限制
  if (getContentLength() + text.length > maxLength.value) {
    return;
  }

  // 插入文本
  document.execCommand('insertText', false, text);

  nextTick(() => {
    updateContent();
    checkMentionTrigger();
  });
};

// 处理失焦事件
const handleBlur = () => {
  // 使用setTimeout确保点击选择用户的事件能被触发
  setTimeout(() => {
    isDropdownVisible.value = false;
    emits('blur');
  }, 150);
};

// 更新内容
const updateContent = () => {
  if (!inputRef.value) return;

  // 提取纯文本内容
  content.value = inputRef.value.innerText;

  // 触发input事件和双向绑定事件
  emits('input', content.value);
  emits('update:modelValue', content.value);
};

// 检查是否触发了提及
const checkMentionTrigger = () => {
  if (!inputRef.value) return;

  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    isDropdownVisible.value = false;
    return;
  }

  const range = selection.getRangeAt(0);
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(inputRef.value);
  preCaretRange.setEnd(range.startContainer, range.startOffset);

  // 获取光标前的文本
  const preCaretText = preCaretRange.toString();

  // 查找最后一个触发字符
  const lastTriggerIndex = preCaretText.lastIndexOf(triggerChar.value);

  if (lastTriggerIndex !== -1) {
    // 检查触发字符后的文本（包括空字符串）
    const afterTriggerText = preCaretText.substring(lastTriggerIndex + 1);

    // 检查触发字符前是否是空格或行首（避免在单词中间触发）
    const beforeTriggerChar = lastTriggerIndex > 0 ? preCaretText[lastTriggerIndex - 1] : ' ';
    const isValidTrigger = beforeTriggerChar === ' ' || beforeTriggerChar === '\n' || lastTriggerIndex === 0;

    // 检查触发字符后是否包含空格（如果包含空格则不触发）
    const hasSpaceAfter = afterTriggerText.includes(' ') || afterTriggerText.includes('\n');

    if (isValidTrigger && !hasSpaceAfter) {
      searchQuery.value = afterTriggerText;
      mentionStartIndex.value = lastTriggerIndex;
      filterUsers();
      isDropdownVisible.value = true;
      selectedUserIndex.value = 0;

      // 计算下拉框位置
      updateDropdownPosition(range);
      return;
    }
  }

  isDropdownVisible.value = false;
};

// 更新下拉框位置
const updateDropdownPosition = (range: Range) => {
  if (!inputRef.value || !dropdownRef.value) return;

  // 获取光标的边界矩形
  const rect = range.getBoundingClientRect();
  const inputRect = inputRef.value.getBoundingClientRect();

  // 计算下拉框的位置（相对于输入框）
  const top = rect.bottom - inputRect.top;
  const left = rect.left - inputRect.left;

  // 设置下拉框样式
  dropdownStyle.value = {
    position: 'absolute',
    top: `${top}px`,
    left: `${left}px`,
    maxWidth: `${inputRect.width - left}px`
  };
};

// 过滤用户
const filterUsers = () => {
  if (!searchQuery.value) {
    filteredUsers.value = props.users;
    return;
  }

  const query = searchQuery.value.toLowerCase();
  filteredUsers.value = props.users.filter(user =>
    user.name.toLowerCase().includes(query) ||
    user.email.toLowerCase().includes(query)
  );
};

// 选择用户
const selectUser = (user: User) => {
  if (!inputRef.value) return;

  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return;

  // 获取当前光标位置
  const range = selection.getRangeAt(0);

  // 获取输入框的全部文本内容
  const fullText = inputRef.value.innerText;

  // 计算光标在全文中的位置
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(inputRef.value);
  preCaretRange.setEnd(range.startContainer, range.startOffset);
  const caretPosition = preCaretRange.toString().length;

  // 找到@符号的位置
  const textBeforeCaret = fullText.substring(0, caretPosition);
  const atIndex = textBeforeCaret.lastIndexOf(triggerChar.value);

  if (atIndex === -1) return;

  // 构建新的文本内容
  const beforeAt = fullText.substring(0, atIndex);
  const afterCaret = fullText.substring(caretPosition);
  const mentionText = `${triggerChar.value}${user.name}`;

  // 创建mention标签的HTML
  const mentionHtml = `<span class="mention" data-user-id="${user.id}" contenteditable="false">${mentionText}</span>`;
  const beforeAtHtml = beforeAt.replace(/\n/g, '<br>');
  const afterCaretHtml = afterCaret.replace(/\n/g, '<br>');
  const newHtml = beforeAtHtml + mentionHtml + '&nbsp;' + afterCaretHtml;

  // 更新输入框内容
  inputRef.value.innerHTML = newHtml;

  // 设置光标位置到mention标签后面
  const newCaretPosition = beforeAt.length + mentionText.length + 1;
  setCaretPosition(newCaretPosition);

  // 更新内容并隐藏下拉框
  isDropdownVisible.value = false;
  nextTick(() => {
    updateContent();
    // 触发mention-select事件
    emits('mention-select', user);
  });
};

// 处理mention标签删除
const handleMentionDeletion = (event: KeyboardEvent): boolean => {
  if (!inputRef.value) return false;

  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return false;

  const range = selection.getRangeAt(0);

  // 如果有选中内容，让默认行为处理
  if (!range.collapsed) return false;

  // 获取光标前的节点
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(inputRef.value);
  preCaretRange.setEnd(range.startContainer, range.startOffset);

  // 检查光标是否紧跟在mention标签后面
  let currentNode = range.startContainer;
  let offset = range.startOffset;

  // 如果光标在文本节点的开头，检查前一个兄弟节点
  if (currentNode.nodeType === Node.TEXT_NODE && offset === 0) {
    const prevSibling = currentNode.previousSibling;
    if (prevSibling && prevSibling.nodeType === Node.ELEMENT_NODE) {
      const element = prevSibling as HTMLElement;
      if (element.classList.contains('mention')) {
        // 删除mention标签
        element.remove();
        event.preventDefault();
        nextTick(() => {
          updateContent();
          checkMentionTrigger();
        });
        return true;
      }
    }
  }

  // 如果光标在mention标签内部，删除整个标签
  let parentElement = currentNode.nodeType === Node.TEXT_NODE ? currentNode.parentElement : currentNode as HTMLElement;
  while (parentElement && parentElement !== inputRef.value) {
    if (parentElement.classList.contains('mention')) {
      // 将光标移动到mention标签前
      const newRange = document.createRange();
      newRange.setStartBefore(parentElement);
      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);

      // 删除mention标签
      parentElement.remove();
      event.preventDefault();
      nextTick(() => {
        updateContent();
        checkMentionTrigger();
      });
      return true;
    }
    parentElement = parentElement.parentElement;
  }

  return false;
};

// 设置光标位置的辅助函数
const setCaretPosition = (position: number) => {
  if (!inputRef.value) return;

  const selection = window.getSelection();
  if (!selection) return;

  let currentPosition = 0;
  const walker = document.createTreeWalker(
    inputRef.value,
    NodeFilter.SHOW_TEXT,
    null
  );

  let node: Node | null;
  while (node = walker.nextNode()) {
    const nodeLength = node.textContent?.length || 0;
    if (currentPosition + nodeLength >= position) {
      const range = document.createRange();
      range.setStart(node, position - currentPosition);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
      return;
    }
    currentPosition += nodeLength;
  }

  // 如果没有找到合适的文本节点，设置到最后
  const range = document.createRange();
  range.selectNodeContents(inputRef.value);
  range.collapse(false);
  selection.removeAllRanges();
  selection.addRange(range);
};

// 获取内容长度
const getContentLength = () => {
  if (!inputRef.value) return 0;
  return inputRef.value.innerText.length;
};

// 截断内容以符合最大长度限制
const truncateContent = () => {
  if (!inputRef.value) return;

  let length = 0;

  // 遍历所有子节点，直到达到最大长度
  for (const node of Array.from(inputRef.value.childNodes)) {
    if (length >= maxLength.value) {
      inputRef.value.removeChild(node);
      continue;
    }

    if (node.nodeType === Node.TEXT_NODE) {
      const nodeText = node.textContent || '';
      const remainingLength = maxLength.value - length;

      if (nodeText.length > remainingLength) {
        node.textContent = nodeText.substring(0, remainingLength);
        length = maxLength.value;
      } else {
        length += nodeText.length;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 对于提及标签，整个保留或不保留
      const nodeLength = (node.textContent || '').length;

      if (length + nodeLength <= maxLength.value) {
        length += nodeLength;
      } else {
        inputRef.value.removeChild(node);
      }
    }
  }

  updateContent();
};

// 监听点击事件，处理提及标签的点击
onMounted(() => {
  if (!inputRef.value) return;

  inputRef.value.addEventListener('click', (event: Event) => {
    const target = event.target as HTMLElement;

    if (target.classList.contains('mention')) {
      const userId = target.getAttribute('data-user-id');

      if (userId) {
        const user = props.users.find(u => u.id.toString() === userId);
        if (user) {
          emits('mention-select', user);
        }
      }
    }
  });
});
</script>

<style scoped>
.mentions-input-container {
  font-family: 'Inter', system-ui, sans-serif;
}

.mentions-input-container .has-value::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 10px;
  width: 8px;
  height: 8px;
  background-color: #4ade80;
  border-radius: 50%;
}

.mentions-input-container [contenteditable]:empty::before {
  content: attr(placeholder);
  color: #94a3b8;
  pointer-events: none;
}

.mentions-input-container [contenteditable] {
  line-height: 1.6;
}

/* 改进输入框的聚焦状态 */
.mentions-input-container [contenteditable]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mention标签样式 - 类似tag的高亮效果 */
:deep(.mention) {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  margin: 0 2px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
}

:deep(.mention:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

:deep(.mention:active) {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

/* 下拉框样式改进 */
.mentions-input-container .z-10 {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 用户列表项悬停效果 */
.mentions-input-container .cursor-pointer:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 选中的用户项 */
.mentions-input-container .bg-primary\/10 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  border-left: 3px solid #3b82f6;
}
</style>