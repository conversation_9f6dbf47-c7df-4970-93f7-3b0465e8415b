# 流程及时交付率分析功能说明

## 功能概述

该功能根据流程数据中的 `flowType`（负责人）字段，计算每个负责人在每月的流程完成状态，并使用 ECharts 渲染到对应的容器中。

## 数据结构

### 输入数据字段
```typescript
interface FlowData {
  flowId: string;           // 流程ID
  flowTemplate: string;     // 流程模板
  flowNumber: string;       // 流程编码
  flowName: string;         // 流程名称
  flowCreator: string;      // 流程发起人
  flowCreateTime: LocalDateTime; // 流程发起时间
  flowPlanDate: LocalDate;  // 流程期望完成时间
  flowType: number[];       // 负责人ID数组
  flowPublishTime: string;  // 流程结束时间（可为空）
  flowStatus: string;       // 流程状态
}
```

## 核心算法

### 1. 流程状态计算
```typescript
const calculateFlowStatus = (flowPlanDate: string, flowPublishTime: string | null): string => {
  if (!flowPublishTime) {
    return '未结束'  // 流程未完成
  }
  
  const planDate = moment(flowPlanDate)
  const publishDate = moment(flowPublishTime)
  const daysDiff = publishDate.diff(planDate, 'days')
  
  // 超期一天内算正常，其它算延期
  if (daysDiff <= 1) {
    return '正常'
  } else {
    return '延期'
  }
}
```

### 2. 数据分组统计
按负责人和月份进行分组统计：
- **正常**：在计划完成时间后1天内完成的流程
- **延期**：超过计划完成时间1天以上完成的流程  
- **未结束**：尚未完成的流程

### 3. 图表渲染
为每个负责人渲染独立的堆叠柱状图，容器ID格式：`person-${dict.value}-line`

## 图表配置

### 颜色方案
- **正常**：绿色 (#67C23A)
- **延期**：红色 (#F56C6C)
- **未结束**：橙色 (#E6A23C)

### 图表特性
- **堆叠柱状图**：同一月份的三种状态堆叠显示
- **响应式设计**：支持不同屏幕尺寸
- **交互提示**：鼠标悬停显示详细数据
- **自动调整**：窗口大小变化时自动调整图表尺寸

## 使用方式

### 1. 容器准备
在模板中为每个负责人创建图表容器：
```vue
<div 
  v-for="dict in getIntDictOptions('information_person')"
  :key="dict.value"
>
  <div 
    :id="`person-${dict.value}-line`" 
    class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
  ></div>
</div>
```

### 2. 数据处理流程
1. 获取流程数据
2. 按负责人和月份分组
3. 计算每种状态的数量
4. 渲染图表到对应容器

### 3. 生命周期管理
- **onMounted**：初始化数据和图表
- **onUnmounted**：清理图表实例和事件监听
- **数据更新**：自动重新渲染图表

## 响应式设计

### 断点设置
- **桌面端**：200px 高度
- **平板端**：180px 高度  
- **手机端**：160px 高度

### 布局适配
```scss
@media (max-width: 768px) {
  [id^="person-"][id$="-line"] {
    height: 180px !important;
    margin-bottom: 16px;
  }
}
```

## 性能优化

### 1. 图表实例管理
- 使用 `Map` 存储图表实例
- 避免重复创建图表
- 及时销毁不需要的实例

### 2. 内存管理
- 使用 `markRaw()` 包装 ECharts 实例
- 组件卸载时清理所有资源
- 移除事件监听器

### 3. 数据处理优化
- 一次性处理所有数据
- 避免重复计算
- 缓存处理结果

## 错误处理

### 1. 容器检查
```typescript
const container = document.getElementById(containerId)
if (!container) {
  console.warn(`Container ${containerId} not found`)
  return
}
```

### 2. 数据验证
- 检查 `flowType` 是否为数组
- 验证日期格式
- 处理空数据情况

### 3. 异常捕获
```typescript
try {
  const res = await FlowApi.getFlowList(queryParams)
  dataList.value = res
} catch (error) {
  console.error('获取流程数据失败:', error)
  message.error('获取流程数据失败')
}
```

## 扩展功能

### 1. 自定义时间范围
支持按年/月查看数据：
```typescript
const queryParams = reactive({
  date: moment().format('YYYY-MM-DD'),
  dateType: 'year' as any
})
```

### 2. 实时更新
负责人变更后自动重新渲染：
```typescript
const changePerson = async (row: any) => {
  await FlowApi.saveFlowPerson({
    id: row.flowId,
    person: row.flowType
  })
  message.success('更新成功')
  await nextTick()
  renderCharts()  // 重新渲染图表
}
```

### 3. 数据导出
可扩展添加图表数据导出功能，支持 PNG、PDF 等格式。

## 注意事项

1. **数据格式**：确保 `flowType` 是数组格式
2. **容器ID**：必须按 `person-${dict.value}-line` 格式命名
3. **字典配置**：需要正确配置 `information_person` 字典
4. **日期格式**：确保日期字段格式正确
5. **权限控制**：根据用户权限显示不同功能
