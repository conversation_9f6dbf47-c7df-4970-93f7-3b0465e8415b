<template>
  <vxe-toolbar custom ref="toolbarRef" size="mini">
    <template #buttons>
      <el-switch
        v-model="noPage"
        @change="onPageChange"
        active-text="不分页"
        inactive-text="分页"
        size="small"
      />
    </template>
    <template #tools>
      <el-button circle @click="uploadRef?.open()" size="small" class="mr-5px !h-28px !w-28px">
        <Icon icon="ep:upload" />
      </el-button>
      <el-button circle @click="exportLinkAll" size="small" class="mr-5px !h-28px !w-28px">
        <Icon icon="ep:download" />
      </el-button>
    </template>
  </vxe-toolbar>
  <div class="h-[calc(100%-100px)]">
    <vxe-table
      ref="tableRef"
      :data="list"
      :header-cell-style="{ padding: 0 }"
      :cell-style="{ padding: 0, height: '30px', color: '#232323' }"
      :filter-config="{ showIcon: false }"
      border
      stripe
      show-overflow
      align="center"
      :loading="loading"
      height="100%"
      :menu-config="menuConfig"
      @menu-click="menuClickEvent"
      :checkbox-config="{ labelField: 'name', highlight: true, range: true }"
      :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
    >
      <vxe-column type="checkbox" width="60" field="name" />
      <vxe-column field="itemCode" width="100">
        <template #header>
          <div>物料编码</div>
          <el-input
            v-model="queryParams.itemCode"
            @change="handleList"
            clearable
            placeholder="按回车筛选"
            style="width: 100%"
            size="small"
          />
        </template>
      </vxe-column>
      <vxe-column field="itemName" width="150">
        <template #header>
          <div>物料名称</div>
          <el-input
            v-model="queryParams.itemName"
            @change="handleList"
            clearable
            placeholder="按回车筛选"
            style="width: 100%"
            size="small"
          />
        </template>
      </vxe-column>
      <vxe-column field="spec">
        <template #header>
          <div>规格</div>
          <el-input
            v-model="queryParams.spec"
            @change="handleList"
            clearable
            placeholder="按回车筛选"
            style="width: 100%"
            size="small"
          />
        </template>
      </vxe-column>
      <vxe-column field="model" width="100">
        <template #header>
          <div>型号</div>
          <el-input
            v-model="queryParams.model"
            @change="handleList"
            clearable
            placeholder="按回车筛选"
            style="width: 100%"
            size="small"
          />
        </template>
      </vxe-column>
      <vxe-column title="工艺路线" field="technologyId" width="200" :edit-render="{}">
        <template #header>
          <div>工艺路线</div>
          <el-select
            v-model="queryParams.technologyId"
            @change="handleList"
            size="small"
            clearable
            style="width: 100%"
          >
            <el-option label="空" :value="-2" />
            <el-option label="非空" :value="-1" />
            <el-option
              v-for="item in routeList"
              :key="item.id"
              :label="`${item.routeCode} ${item.routeName}`"
              :value="item.id"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <div>{{ row.technologyName }}</div>
        </template>
        <template #edit="{ row }">
          <el-select v-model="row.technologyId">
            <el-option
              v-for="item in routeList"
              :key="item.id"
              :label="`${item.routeCode} ${item.routeName}`"
              :value="item.id"
            />
          </el-select>
        </template>
      </vxe-column>
      <vxe-column title="标准工时" filed="timeConsuming" width="200" :edit-render="{}">
        <template #default="{ row }">
          {{ row.timeConsuming }}
        </template>
        <template #edit="{ row }">
          <el-input-number v-model="row.timeConsuming" :min="0" :precision="2" />
        </template>
      </vxe-column>
      <vxe-column title="最后修改人" width="160" field="updateName" />
      <vxe-column title="最后修改时间" width="160" field="updateTime">
        <template #default="{ row }">
          {{ row.updateTime&&formatToDateTime(row.updateTime) }}
        </template>
      </vxe-column>
      <vxe-column title="操作" width="160">
        <template #default="{ row }">
          <template v-if="hasEditStatus(row)">
            <vxe-button @click="saveRowEvent(row)">保存</vxe-button>
            <vxe-button @click="cancelRowEvent()">取消</vxe-button>
          </template>
          <template v-else>
            <vxe-button @click="editRowEvent(row)">编辑</vxe-button>
          </template>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <!-- 分页 -->
  <Pagination
    :total="total"
    v-model:page="queryParams.pageNo"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
  <Dialog title="按选中数据设置工艺路线" v-model="selectionVisiable">
    <div>当前选中：{{ selectionData.length }} 条数据</div>
    <el-form label-width="100px">
      <el-form-item label="设置工艺路线">
        <el-select v-model="selectionTechnologyId" filterable :clearable="false">
          <el-option
            v-for="item in routeList"
            :key="item.id"
            :label="`${item.routeCode} ${item.routeName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设置标准工时">
        <el-input-number v-model="timeConsuming" :min="0" :precision="2" class="!w-100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSaveSelection">保存</el-button>
    </template>
  </Dialog>

  <Dialog title="按条件设置工艺路线" v-model="conditionsVisiable">
    <el-form label-width="100px" v-loading="conditionLoading">
      <el-form-item label="物料编码">
        <el-select class="!w-30%" v-model="conditionsData.itemCodeCondition">
          <el-option
            v-for="item in conditionsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input class="!w-70%" v-model="conditionsData.itemCode" />
      </el-form-item>
      <el-form-item label="物料名称">
        <el-select class="!w-30%" v-model="conditionsData.itemNameCondition">
          <el-option
            v-for="item in conditionsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input class="!w-70%" v-model="conditionsData.itemName" />
      </el-form-item>
      <el-form-item label="规格">
        <el-select class="!w-30%" v-model="conditionsData.specCondition">
          <el-option
            v-for="item in conditionsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input class="!w-70%" v-model="conditionsData.spec" />
      </el-form-item>
      <el-form-item label="型号">
        <el-select class="!w-30%" v-model="conditionsData.modelCondition">
          <el-option
            v-for="item in conditionsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input class="!w-70%" v-model="conditionsData.model" />
      </el-form-item>
      <el-form-item label="工艺路线">
        <el-select class="!w-30%" v-model="conditionsData.technologyCondition">
          <el-option label="空" value="isNull" />
          <el-option label="非空" value="nonNull" />
          <el-option label="等于" value="equal" />
          <el-option label="不等于" value="notEqual" />
        </el-select>
        <el-select
          class="!w-70%"
          v-model="conditionsData.technologyId"
          :disabled="['isNull', 'nonNull'].includes(conditionsData.technologyCondition)"
        >
          <el-option
            v-for="item in routeList"
            :key="item.id"
            :label="`${item.routeCode} ${item.routeName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-divider>当前符合条件数量：{{ conditionsCount }}</el-divider>

      <el-form-item label="设置工艺路线">
        <el-select class="!w-100%" v-model="conditionsData.setTechnologyId">
          <el-option
            v-for="item in routeList"
            :key="item.id"
            :label="`${item.routeCode} ${item.routeName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设置标准工时">
        <el-input-number
          v-model="conditionsData.timeConsuming"
          :min="0"
          :precision="2"
          class="!w-100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="warning" @click="onSelectLinkCount" :loading="conditionLoading">
        验证条件
      </el-button>
      <el-button
        type="warning"
        @click="exportLink"
        :loading="conditionLoading"
        v-if="conditionsCount > 0"
        plain
      >
        导出查看
      </el-button>
      <el-button
        type="primary"
        :loading="conditionLoading"
        v-if="conditionsCount > 0"
        @click="saveLinkConditions"
      >
        保存
      </el-button>
    </template>
  </Dialog>

  <RouteUpload ref="uploadRef" @success="handleList" />
</template>

<script lang="ts" setup>
import { RouteApi } from '@/api/report/technology/route'
import download from '@/utils/download'
import { cloneDeep } from 'lodash-es'
import RouteUpload from './RouteUpload.vue'
import { formatToDateTime } from '@/utils/dateUtil'

const tableRef = ref()
const toolbarRef = ref()
const uploadRef = ref()
const selectionVisiable = ref(false)
const selectionData = ref<any[]>([])
const selectionTechnologyId = ref<number | undefined>(undefined)
const timeConsuming = ref<number | undefined>(undefined)

const conditionsVisiable = ref(false)

const queryParams = ref({
  pageNo: 1,
  pageSize: 20,
  itemCode: '',
  itemName: '',
  spec: '',
  model: '',
  technologyId: undefined
})

const conditionsList = reactive([
  { label: '等于', value: 'equal' },
  { label: '不等于', value: 'notEqual' },
  { label: '包含', value: 'like' },
  { label: '不包含', value: 'notLike' },
  { label: '左匹配', value: 'likeLeft' },
  { label: '右匹配', value: 'likeRight' }
])

const conditionsData = ref({
  itemCodeCondition: 'likeLeft',
  itemCode: '',
  itemNameCondition: 'like',
  itemName: '',
  specCondition: 'like',
  spec: '',
  modelCondition: 'equal',
  model: '',
  technologyCondition: 'equal',
  technologyId: undefined,
  setTechnologyId: undefined,
  timeConsuming: undefined
})

const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)
const noPage = ref(false)
const message = useMessage()
const routeList = ref<any[]>([])
const conditionsCount = ref(0)
const conditionLoading = ref(false)

const menuConfig = reactive<any>({
  body: {
    options: [
      [
        { code: 'selected', name: '按选中设置工艺路线' },
        { code: 'conditions', name: '按条件设置工艺路线' }
      ]
    ]
  }
})

const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'selected':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要设置的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      selectionVisiable.value = true
      break
    case 'conditions':
      conditionsVisiable.value = true
      break
  }
}

const onSelectLinkCount = async () => {
  conditionLoading.value = true
  try {
    const res = await RouteApi.selectLinkCount(conditionsData.value)
    conditionsCount.value = res
  } finally {
    conditionLoading.value = false
  }
}

const exportLink = async () => {
  conditionLoading.value = true
  try {
    const res = await RouteApi.exportLink(conditionsData.value)
    download.excel(res, '符合条件物料列表')
  } finally {
    conditionLoading.value = false
  }
}

const exportLinkAll = async () => {
  conditionLoading.value = true
  try {
    let query = cloneDeep(queryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const res = await RouteApi.exportLinkAll(query)
    download.excel(res, '所有物料列表')
  } finally {
    conditionLoading.value = false
  }
}

const saveLinkConditions = async () => {
  conditionLoading.value = true
  try {
    await message.confirm('确定批量修改物料工艺路线？')
    if (!conditionsData.value.setTechnologyId) {
      return message.alertError('请选择要设置的工艺路线')
    }
    await RouteApi.saveLinkConditions(conditionsData.value)
    message.success('保存成功')
    conditionsVisiable.value = false
    refreshConditions()
    handleList()
  } finally {
    conditionLoading.value = false
  }
}

const refreshConditions = () => {
  conditionsData.value = {
    itemCodeCondition: 'equal',
    itemCode: '',
    itemNameCondition: 'equal',
    itemName: '',
    specCondition: 'equal',
    spec: '',
    modelCondition: 'equal',
    model: '',
    technologyCondition: 'equal',
    technologyId: undefined,
    setTechnologyId: undefined,
    timeConsuming: undefined
  }
}
const onPageChange = () => {
  if (noPage.value) {
    if (total.value > 1000) {
      message.alertError('列表数据量过大，请分页查询')
      noPage.value = false
      return
    }
    queryParams.value.pageSize = -1
  } else {
    queryParams.value.pageSize = 20
  }
  handleList()
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

const getList = async () => {
  loading.value = true
  try {
    let query = cloneDeep(queryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const res = await RouteApi.getRouteLinkPage(query)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const getRouteList = async () => {
  loading.value = true
  try {
    const res = await RouteApi.getRouteAllVersionList()
    routeList.value = res
  } finally {
    loading.value = false
  }
}

const onSaveSelection = async () => {
  loading.value = true
  try {
    let data: any[] = []
    selectionData.value.forEach((item: any) => {
      data.push({
        id: item.id,
        itemCode: item.itemCode,
        technologyId: selectionTechnologyId.value,
        timeConsuming: timeConsuming.value
      })
    })
    await RouteApi.saveLink(data)
    message.success('保存成功')
    getList()
    selectionVisiable.value = false
    selectionData.value = []
    selectionTechnologyId.value = undefined
    timeConsuming.value = undefined
  } finally {
    loading.value = false
  }
}

const setQueryParams = (id: number) => {
  requestAnimationFrame(() => {
    queryParams.value.technologyId = id as unknown as undefined
    setTimeout(() => {
      handleList()
    }, 500)
  })
}

const hasEditStatus = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    return $table.isEditByRow(row)
  }
}

const editRowEvent = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    $table.setEditRow(row)
  }
}

const saveRowEvent = (row: any) => {
  const $table = tableRef.value
  loading.value = true
  try {
    if ($table) {
      $table.clearEdit().then(async () => {
        let data = {
          id: row.id,
          itemCode: row.itemCode,
          technologyId: row.technologyId,
          timeConsuming: row.timeConsuming
        }
        await RouteApi.saveLink([data])
        message.success('保存成功')
        getList()
      })
    }
  } finally {
    loading.value = false
  }
}

const cancelRowEvent = () => {
  const $table = tableRef.value
  if ($table) {
    $table.clearEdit()
  }
}

defineExpose({
  setQueryParams
})

onMounted(() => {
  getRouteList()
  handleList()
  unref(tableRef).connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 0 !important;
}

:deep(.row--stripe) {
  background-color: #f9f9f9 !important;
}

:deep(.vxe-cell--edit-icon) {
  display: none !important;
}
</style>
