<template>
  <ContentWrap>
    <el-tabs v-model="currentTab">
      <el-tab-pane label="部门看板" name="main" />
      <el-tab-pane label="项目及时交付率详情" name="TimelyDelivery" />
      <el-tab-pane label="订单交付率详情" name="OrderDelivery" />
      <el-tab-pane label="各部门服务满意度详情" name="RedFlower" />
    </el-tabs>
    <TimelyDelivery v-if="currentTab === 'TimelyDelivery'" />
    <OrderDelivery v-else-if="currentTab === 'OrderDelivery'" />
    <RedFlower v-else-if="currentTab === 'RedFlower'" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import TimelyDelivery from './componets/TimelyDelivery.vue'
import OrderDelivery from './componets/OrderDelivery.vue';
import RedFlower from './componets/RedFlower.vue';

const currentTab = ref('RedFlower')
</script>
