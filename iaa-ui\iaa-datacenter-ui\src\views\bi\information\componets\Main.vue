<template>
  <div class="h-[calc(100vh-180px)]">
    <el-form inline class="custom-form" size="small">
      <el-date-picker
        v-model="queryParams.year"
        type="year"
        placeholder="选择年份"
        @change="onItList"
        :clearable="false"
      />
    </el-form>
    <el-row>
      <el-col
        v-for="dict in getStrDictOptions('IT_Indicator')"
        :key="dict.value"
        :span="6"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        :xl="6"
      >
        <div
          :id="dict.value"
          class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
        ></div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { DeptIndicatorApi } from '@/api/hr/deptindicator'
import { getStrDictOptions } from '@/utils/dict'

const queryParams = reactive({
  dept: 'IT',
  year: moment().format('YYYY')
})

const itList = ref<any[]>([])
const onItList = async () => {
  const res = await DeptIndicatorApi.getDeptIndicatorList(queryParams)
  itList.value = res
}

onMounted(() => {
  onItList()
})
</script>
