<template>
  <vxe-toolbar ref="toolbarRef" custom size="mini">
    <template #buttons>
      <el-button type="primary" size="small" plain @click="handleList">查询</el-button>
      <el-button type="warning" size="small" plain @click="refreshQuery">清空筛选</el-button>
    </template>
    <template #tools>
      <el-switch
        active-text="隐藏已完成"
        inactive-text="显示所有"
        size="small"
        v-model="queryParams.showAll"
        @change="handleList"
      />
    </template>
  </vxe-toolbar>
  <div class="h-[calc(100vh-260px)]">
    <vxe-table
      :row-config="{ height: 30 }"
      id="instructionTable"
      :custom-config="customConfig"
      :header-cell-style="{ padding: 0, height: '30px' }"
      :cell-style="{ padding: 0, height: '30px' }"
      :column-config="{ resizable: true }"
      :virtual-y-config="{ enabled: true, gt: 0 }"
      align="center"
      border
      show-overflow
      height="100%"
      :data="dataList"
      ref="tableRef"
      size="small"
      :row-style="rowStyle"
      @cell-click="(el: any) => detailFormRef?.openForm(el.row)"
      :loading="loading"
    >
      <vxe-column field="businessDate" width="200" title="日期">
        <template #header>
          <div>日期</div>
          <el-date-picker
            v-model="queryParams.businessDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="seller" width="100" title="业务员">
        <template #header>
          <div>业务员</div>
          <el-input
            v-model="queryParams.seller"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="customerName" width="120" title="客户">
        <template #header>
          <div>客户</div>
          <el-input
            v-model="queryParams.customerName"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      
      <vxe-column field="itemCode" width="120" title="品号">
        <template #header>
          <div>品号</div>
          <el-input
            v-model="queryParams.itemCode"
            clearable
            placeholder="左查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="itemName" width="140" title="品名">
        <template #header>
          <div>品名</div>
          <el-input
            v-model="queryParams.itemName"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="spec" min-width="200" title="规格">
        <template #header>
          <div>规格</div>
          <el-input
            v-model="queryParams.spec"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="custom" width="120" title="定制内容">
        <template #header>
          <div>定制内容</div>
          <el-select size="small" v-model="queryParams.custom" clearable multiple collapse-tags>
            <el-option
              v-for="dict in getStrDictOptions('eng_instruction_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          {{ getDictLabel('eng_instruction_dict', row.custom) }}
        </template>
      </vxe-column>
      <vxe-column field="description" width="200" title="任务">
        <template #header>
          <div>任务</div>
          <el-input
            v-model="queryParams.description"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="planReceiptDate" width="200" title="计划接收日期">
        <template #header>
          <div>计划接收日期</div>
          <el-date-picker
            v-model="queryParams.planReceiptDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualReceiptDate" width="200" title="实际接收日期">
        <template #header>
          <div>实际接收日期</div>
          <el-date-picker
            v-model="queryParams.actualReceiptDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="planDesignDate" width="200" title="计划设计日期">
        <template #header>
          <div>计划设计日期</div>
          <el-date-picker
            v-model="queryParams.planDesignDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualDesignDate" width="200" title="实际设计日期">
        <template #header>
          <div>实际设计日期</div>
          <el-date-picker
            v-model="queryParams.actualDesignDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <!-- <vxe-column field="planQuotationDate" width="200" title="计划报价日期">
        <template #header>
          <div>计划报价日期</div>
          <el-date-picker
            v-model="queryParams.planQuotationDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualQuotationDate" width="200" title="实际报价日期">
        <template #header>
          <div>实际报价日期</div>
          <el-date-picker
            v-model="queryParams.actualQuotationDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column> -->
      <vxe-column field="planTestingDate" width="200" title="计划打样日期">
        <template #header>
          <div>计划打样日期</div>
          <el-date-picker
            v-model="queryParams.planTestingDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualTestingDate" width="200" title="实际打样日期">
        <template #header>
          <div>实际打样日期</div>
          <el-date-picker
            v-model="queryParams.actualTestingDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="planAdmitDate" width="200" title="计划确认日期">
        <template #header>
          <div>计划确认日期</div>
          <el-date-picker
            v-model="queryParams.planAdmitDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualAdmitDate" width="200" title="实际确认日期">
        <template #header>
          <div>实际确认日期</div>
          <el-date-picker
            v-model="queryParams.actualAdmitDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <!-- <vxe-column field="planBomDate" width="200" title="计划BOM搭建日期">
        <template #header>
          <div>计划BOM搭建日期</div>
          <el-date-picker
            v-model="queryParams.planBomDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualBomDate" width="200" title="实际BOM搭建日期">
        <template #header>
          <div>实际BOM搭建日期</div>
          <el-date-picker
            v-model="queryParams.actualBomDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column> -->
      <vxe-column field="planCompleteDate" width="200" title="计划完成日期">
        <template #header>
          <div>计划完成日期</div>
          <el-date-picker
            v-model="queryParams.planCompleteDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualCompleteDate" width="200" title="实际完成日期">
        <template #header>
          <div>实际完成日期</div>
          <el-date-picker
            v-model="queryParams.actualCompleteDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="remark" width="200" title="备注">
        <template #header>
          <div>备注</div>
          <el-input
            v-model="queryParams.remark"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <!-- 分页 -->
  <Pagination
    v-model:limit="queryParams.pageSize"
    v-model:page="queryParams.pageNo"
    :total="total"
    @pagination="getList"
    size="small"
  />
  <DetailForm ref="detailFormRef" @success="handleList" />
</template>

<script lang="ts" setup>
import { CustomerApi } from '@/api/report/erp/order-bom'
import DetailForm from './DetailForm.vue'
import { customConfig } from '@/utils/vxeCustom'
import { getDictLabel, getStrDictOptions } from '@/utils/dict'
import moment from 'moment'

const toolbarRef = ref()
const tableRef = ref()
const detailFormRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 100,
  type: 'instruction',
  businessDate: undefined,
  seller: undefined,
  customerName: undefined,
  description: undefined,
  custom: undefined,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  planReceiptDate: undefined,
  actualReceiptDate: undefined,
  planDesignDate: undefined,
  actualDesignDate: undefined,
  planQuotationDate: undefined,
  actualQuotationDate: undefined,
  planTestingDate: undefined,
  actualTestingDate: undefined,
  planAdmitDate: undefined,
  actualAdmitDate: undefined,
  planBomDate: undefined,
  actualBomDate: undefined,
  planCompleteDate: undefined,
  actualCompleteDate: undefined,
  remark: undefined,
  showAll: true
})
const dataList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)

const getList = async () => {
  loading.value = true
  try {
    const res = await CustomerApi.pageDetail(queryParams.value)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

const rowStyle: any = ({ row }) => {
  if (!row['planCompleteDate']) {
    return {
      cursor: 'pointer'
    }
  }

  const planDate = moment(row.planCompleteDate)
  let actualDate = row.actualCompleteDate ? moment(row.actualCompleteDate) : moment()
  const planStart = planDate.startOf('day')
  const actualStart = actualDate.startOf('day')

  const daysDiff = planStart.diff(actualStart, 'days')
  if (!row.actualCompleteDate) {
    // 当前日期与计划完成日期比较
    if (daysDiff >= 2) {
      return {
        cursor: 'pointer'
      }
    } else if (daysDiff >= 0) {
      return {
        backgroundColor: 'var(--el-color-warning-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    } else {
      return {
        backgroundColor: 'var(--el-color-error-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    }
  } else {
    // 实际完成日期与计划完成日期比较
    if (daysDiff >= 0) {
      return {
        backgroundColor: 'var(--el-color-success-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    } else {
      return {
        backgroundColor: 'var(--el-color-error-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    }
  }
}

const refreshQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 100,
    type: 'instruction',
    seller: undefined,
    businessDate: undefined,
    customerName: undefined,
    description: undefined,
    custom: undefined,
    itemCode: undefined,
    itemName: undefined,
    spec: undefined,
    planReceiptDate: undefined,
    actualReceiptDate: undefined,
    planDesignDate: undefined,
    actualDesignDate: undefined,
    planQuotationDate: undefined,
    actualQuotationDate: undefined,
    planTestingDate: undefined,
    actualTestingDate: undefined,
    planAdmitDate: undefined,
    actualAdmitDate: undefined,
    planBomDate: undefined,
    actualBomDate: undefined,
    planCompleteDate: undefined,
    actualCompleteDate: undefined,
    remark: undefined,
    showAll: true
  }
  handleList()
}

onMounted(() => {
  handleList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>
