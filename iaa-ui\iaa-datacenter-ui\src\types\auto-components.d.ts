/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppLinkInput: typeof import('./../components/AppLinkInput/index.vue')['default']
    AppLinkSelectDialog: typeof import('./../components/AppLinkInput/AppLinkSelectDialog.vue')['default']
    Backtop: typeof import('./../components/Backtop/src/Backtop.vue')['default']
    CardTitle: typeof import('./../components/Card/src/CardTitle.vue')['default']
    ColorInput: typeof import('./../components/ColorInput/index.vue')['default']
    ComponentContainer: typeof import('./../components/DiyEditor/components/ComponentContainer.vue')['default']
    ComponentContainerProperty: typeof import('./../components/DiyEditor/components/ComponentContainerProperty.vue')['default']
    ComponentLibrary: typeof import('./../components/DiyEditor/components/ComponentLibrary.vue')['default']
    ConditionNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/ConditionNodeConfig.vue')['default']
    ConfigGlobal: typeof import('./../components/ConfigGlobal/src/ConfigGlobal.vue')['default']
    ContentDetailWrap: typeof import('./../components/ContentDetailWrap/src/ContentDetailWrap.vue')['default']
    ContentWrap: typeof import('./../components/ContentWrap/src/ContentWrap.vue')['default']
    CopperModal: typeof import('./../components/Cropper/src/CopperModal.vue')['default']
    CopyTaskNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/CopyTaskNode.vue')['default']
    CopyTaskNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/CopyTaskNodeConfig.vue')['default']
    CountTo: typeof import('./../components/CountTo/src/CountTo.vue')['default']
    Crontab: typeof import('./../components/Crontab/src/Crontab.vue')['default']
    Cropper: typeof import('./../components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./../components/Cropper/src/CropperAvatar.vue')['default']
    DateRange: typeof import('./../components/DateRange/index.vue')['default']
    Descriptions: typeof import('./../components/Descriptions/src/Descriptions.vue')['default']
    DescriptionsItemLabel: typeof import('./../components/Descriptions/src/DescriptionsItemLabel.vue')['default']
    Dialog: typeof import('./../components/Dialog/src/Dialog.vue')['default']
    DictSelect: typeof import('./../components/FormCreate/src/components/DictSelect.vue')['default']
    DictTag: typeof import('./../components/DictTag/src/DictTag.vue')['default']
    DiyEditor: typeof import('./../components/DiyEditor/index.vue')['default']
    DocAlert: typeof import('./../components/DocAlert/index.vue')['default']
    Draggable: typeof import('./../components/Draggable/index.vue')['default']
    Echart: typeof import('./../components/Echart/src/Echart.vue')['default']
    Editor: typeof import('./../components/Editor/src/Editor.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElementBaseInfo: typeof import('./../components/bpmnProcessDesigner/package/penal/base/ElementBaseInfo.vue')['default']
    ElementForm: typeof import('./../components/bpmnProcessDesigner/package/penal/form/ElementForm.vue')['default']
    ElementListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ElementListeners.vue')['default']
    ElementMultiInstance: typeof import('./../components/bpmnProcessDesigner/package/penal/multi-instance/ElementMultiInstance.vue')['default']
    ElementOtherConfig: typeof import('./../components/bpmnProcessDesigner/package/penal/other/ElementOtherConfig.vue')['default']
    ElementProperties: typeof import('./../components/bpmnProcessDesigner/package/penal/properties/ElementProperties.vue')['default']
    ElementTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/ElementTask.vue')['default']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EndEventNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/EndEventNode.vue')['default']
    Error: typeof import('./../components/Error/src/Error.vue')['default']
    ExclusiveNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/ExclusiveNode.vue')['default']
    FilterInput: typeof import('./../components/FilterInput/index.vue')['default']
    FlowCondition: typeof import('./../components/bpmnProcessDesigner/package/penal/flow-condition/FlowCondition.vue')['default']
    Form: typeof import('./../components/Form/src/Form.vue')['default']
    Highlight: typeof import('./../components/Highlight/src/Highlight.vue')['default']
    Icon: typeof import('./../components/Icon/src/Icon.vue')['default']
    IconSelect: typeof import('./../components/Icon/src/IconSelect.vue')['default']
    IFrame: typeof import('./../components/IFrame/src/IFrame.vue')['default']
    ImageViewer: typeof import('./../components/ImageViewer/src/ImageViewer.vue')['default']
    Infotip: typeof import('./../components/Infotip/src/Infotip.vue')['default']
    InputPassword: typeof import('./../components/InputPassword/src/InputPassword.vue')['default']
    InputWithColor: typeof import('./../components/InputWithColor/index.vue')['default']
    MagicCubeEditor: typeof import('./../components/MagicCubeEditor/index.vue')['default']
    MarkdownView: typeof import('./../components/MarkdownView/index.vue')['default']
    MentionsInput: typeof import('./../views/Home/components/MentionsInput.vue')['default']
    NodeHandler: typeof import('./../components/SimpleProcessDesignerV2/src/NodeHandler.vue')['default']
    NoModalDrawer: typeof import('./../components/NoModalDrawer/index.vue')['default']
    OperateLogV2: typeof import('./../components/OperateLogV2/src/OperateLogV2.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    ParallelNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/ParallelNode.vue')['default']
    ProcessDesigner: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessDesigner.vue')['default']
    ProcessExpressionDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ProcessExpressionDialog.vue')['default']
    ProcessListenerDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ProcessListenerDialog.vue')['default']
    ProcessNodeTree: typeof import('./../components/SimpleProcessDesignerV2/src/ProcessNodeTree.vue')['default']
    ProcessPalette: typeof import('./../components/bpmnProcessDesigner/package/palette/ProcessPalette.vue')['default']
    ProcessViewer: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessViewer.vue')['default']
    PropertiesPanel: typeof import('./../components/bpmnProcessDesigner/package/penal/PropertiesPanel.vue')['default']
    Qrcode: typeof import('./../components/Qrcode/src/Qrcode.vue')['default']
    README: typeof import('./../components/TagInput/README.md')['default']
    ReceiveTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ReceiveTask.vue')['default']
    RightMenu: typeof import('./../components/XTable/RightMenu.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterSearch: typeof import('./../components/RouterSearch/index.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    ScriptTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ScriptTask.vue')['default']
    Search: typeof import('./../components/Search/src/Search.vue')['default']
    ShortcutDateRangePicker: typeof import('./../components/ShortcutDateRangePicker/index.vue')['default']
    SignalAndMessage: typeof import('./../components/bpmnProcessDesigner/package/penal/signal-message/SignalAndMessage.vue')['default']
    SimpleProcessDesigner: typeof import('./../components/SimpleProcessDesignerV2/src/SimpleProcessDesigner.vue')['default']
    StartUserNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/StartUserNode.vue')['default']
    StartUserNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/StartUserNodeConfig.vue')['default']
    Sticky: typeof import('./../components/Sticky/src/Sticky.vue')['default']
    SummaryCard: typeof import('./../components/SummaryCard/index.vue')['default']
    Table: typeof import('./../components/Table/src/Table.vue')['default']
    TableSelectForm: typeof import('./../components/Table/src/TableSelectForm.vue')['default']
    TagInput: typeof import('./../components/TagInput/index.vue')['default']
    'TagInput使用指南': typeof import('./../../TagInput使用指南.md')['default']
    TagInputDemo: typeof import('./../views/TagInputDemo.vue')['default']
    Tooltip: typeof import('./../components/Tooltip/src/Tooltip.vue')['default']
    UploadFile: typeof import('./../components/UploadFile/src/UploadFile.vue')['default']
    UploadImg: typeof import('./../components/UploadFile/src/UploadImg.vue')['default']
    UploadImgs: typeof import('./../components/UploadFile/src/UploadImgs.vue')['default']
    UserTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/UserTask.vue')['default']
    UserTaskListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/UserTaskListeners.vue')['default']
    UserTaskNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/UserTaskNode.vue')['default']
    UserTaskNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/UserTaskNodeConfig.vue')['default']
    Verify: typeof import('./../components/Verifition/src/Verify.vue')['default']
    VerifyPoints: typeof import('./../components/Verifition/src/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verifition/src/Verify/VerifySlide.vue')['default']
    VerticalButtonGroup: typeof import('./../components/VerticalButtonGroup/index.vue')['default']
    XButton: typeof import('./../components/XButton/src/XButton.vue')['default']
    XTable: typeof import('./../components/XTable/index.vue')['default']
    XTextButton: typeof import('./../components/XButton/src/XTextButton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
